# MediPaka Backend

MediPaka 是一个全功能的媒体自动化系统，支持订阅管理、资源搜索、自动下载、文件重命名和云存储上传。

## 功能特性

### 📡 订阅模块

- 解析订阅源（如 Mikan RSS）
- 定时抓取更新数据
- 支持用户订阅的番剧/影视条目
- 支持通过 Emby 查询电视剧的遗漏集

### 🔍 资源搜索模块

- 自定义站源匹配规则
- Telegram 频道搜索接口
- Nullbr、Rarbg 等资源站点接口
- 内置正则规则用于提取关键字段

### 💾 网盘转存 & 实时监控模块

- 下载完成自动转存至 Alist/115/OneDrive
- 定时任务或监听文件夹变化实现实时上传
- 支持使用 hash 比对去重、校验文件名

### 🧲 下载器控制模块

- 管理种子上传、下载状态查询
- 多下载器兼容（QB/TR/Aria2/Alist/各种网盘）
- 下载完毕自动回传事件至通知/转存模块

### ✍️ TMDB 资源重命名模块

- 根据 TMDB 元数据重命名剧集/电影
- 支持识别片源版本、清晰度、季/集等
- 支持重命名模版

### 🔔 通知系统

- 支持 Webhook 推送
- 支持 Telegram / 微信 Bot 消息推送
- 支持 Emby 刷新元数据接口调用

## 技术栈

- **后端框架**: FastAPI（支持异步、插件化）
- **任务调度**: APScheduler + Celery（可选）
- **数据库**: SQLite/PostgreSQL
- **消息推送**: Webhook / Telegram / 企业微信
- **下载模块**: Qb、Tr、aria2
- **存储接入**: 模块化网盘插件

## 安装和运行

### 开发环境

```bash
# 安装依赖
pip install -e .

# 启动开发服务器
python start.py --env dev --reload
```

### 生产环境

```bash
# 安装依赖
pip install -e .

# 启动生产服务器
python start.py --env prod
```

## API 文档

启动服务后访问：

- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。
