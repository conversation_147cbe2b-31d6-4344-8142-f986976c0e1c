"""
TMDB (The Movie Database)
"""

from difflib import SequenceMatcher
from typing import Dict, Optional, Any, List

from loguru import logger as logger
from tmdbv3api import TMDb, Movie, TV, Search, Season

from backend.utils.config import settings
from backend.utils import Singleton


class TMDBError(Exception):
    """TMDB API错误"""
    pass


class TMDBService(Singleton):
    """TMDB服务类"""

    def __init__(self):
        # 初始化tmdbv3api
        self.tmdb = TMDb()
        self.tmdb.cache = True
        self.tmdb.api_key = settings.tmdb_api_key or '6257247fc187f780acb9de9312973179'
        self.tmdb.language = settings.tmdb_language or "zh-CN"

        # 初始化各个模块
        self.search = Search()
        self.movie = Movie()
        self.tv = TV()
        self.season = Season()

        # 图片尺寸配置
        self.POSTER_SIZE = "w500"  # 可选: w92, w154, w185, w342, w500, w780, original
        self.BACKDROP_SIZE = "w1280"  # 可选: w300, w780, w1280, original
        self.STILL_SIZE = "w300"  # 剧集截图尺寸

        if not self.tmdb.api_key:
            logger.warning("TMDB API密钥未配置，TMDB功能将不可用")
        else:
            logger.info(f"TMDB服务已初始化，语言: {self.tmdb.language}")

        self._initialized = True

    @property
    def is_configured(self) -> bool:
        """检查TMDB是否已配置"""
        return bool(self.tmdb.api_key)

    def _verify_api_key(self) -> bool:
        """验证API key是否有效"""
        if not self.tmdb.api_key:
            return False

        try:
            # 使用search接口来验证key
            results = self.search.movies("test")
            return results is not None
        except Exception as e:
            logger.error(f"验证API key时发生错误: {str(e)}")
            return False

    def _calculate_similarity(self, s1: str, s2: str) -> float:
        """计算两个字符串的相似度"""
        if not s1 or not s2:
            return 0.0
        return SequenceMatcher(None, s1.lower(), s2.lower()).ratio()

    def _extract_year_from_date(self, date_str: Optional[str]) -> Optional[int]:
        """从日期字符串中提取年份"""
        if not date_str:
            return None
        try:
            return int(date_str.split('-')[0])
        except Exception:
            return None

    def search_movie(self, query: str, year: Optional[int] = None) -> List[Dict[str, Any]]:
        """搜索电影"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return []

            results = self.search.movies(query)
            if not results:
                logger.warning(f"未找到相关电影: {query}")
                return []

            movies = []
            for movie in results:
                try:
                    movie_data = {
                        'id': getattr(movie, 'id', None),
                        'title': getattr(movie, 'title', ''),
                        'original_title': getattr(movie, 'original_title', ''),
                        'release_date': getattr(movie, 'release_date', ''),
                        'year': self._extract_year_from_date(getattr(movie, 'release_date', '')),
                        'overview': getattr(movie, 'overview', ''),
                        'poster_path': self.get_image_url(movie.get('poster_path'), self.POSTER_SIZE),
                        'backdrop_path': self.get_image_url(movie.get('backdrop_path'), self.BACKDROP_SIZE),
                        'vote_average': getattr(movie, 'vote_average', 0.0),
                        'vote_count': getattr(movie, 'vote_count', 0),
                        'popularity': getattr(movie, 'popularity', 0.0),
                        'adult': getattr(movie, 'adult', False),
                        'media_type': 'movie'
                    }

                    # 年份过滤
                    if year and movie_data['year'] and abs(movie_data['year'] - year) > 1:
                        continue

                    movies.append(movie_data)
                except Exception as e:
                    logger.error(f"处理电影数据时发生错误: {str(e)}")
                    continue

            # 按相似度和评分排序
            movies.sort(key=lambda x: (
                self._calculate_similarity(query, x['title']),
                x['vote_average']
            ), reverse=True)

            return movies

        except Exception as e:
            logger.error(f"搜索电影时发生错误: {str(e)}")
            return []

    def search_tv(self, query: str, year: Optional[int] = None) -> List[Dict[str, Any]]:
        """搜索电视剧"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return []

            results = self.search.tv_shows(query)
            if not results:
                logger.warning(f"未找到相关电视剧: {query}")
                return []

            tv_shows = []
            for tv in results:
                try:
                    tv_data = {
                        'id': getattr(tv, 'id', None),
                        'name': getattr(tv, 'name', ''),
                        'original_name': getattr(tv, 'original_name', ''),
                        'first_air_date': getattr(tv, 'first_air_date', ''),
                        'year': self._extract_year_from_date(getattr(tv, 'first_air_date', '')),
                        'overview': getattr(tv, 'overview', ''),
                        'poster_path': self.get_image_url(tv.get('poster_path'), self.POSTER_SIZE),
                        'backdrop_path': self.get_image_url(tv.get('backdrop_path'), self.BACKDROP_SIZE),
                        'vote_average': getattr(tv, 'vote_average', 0.0),
                        'vote_count': getattr(tv, 'vote_count', 0),
                        'popularity': getattr(tv, 'popularity', 0.0),
                        'origin_country': getattr(tv, 'origin_country', []),
                        'media_type': 'tv'
                    }

                    # 年份过滤
                    if year and tv_data['year'] and abs(tv_data['year'] - year) > 1:
                        continue

                    tv_shows.append(tv_data)
                except Exception as e:
                    logger.error(f"处理电视剧数据时发生错误: {str(e)}")
                    continue

            # 按相似度和评分排序
            tv_shows.sort(key=lambda x: (
                self._calculate_similarity(query, x['name']),
                x['vote_average']
            ), reverse=True)

            return tv_shows

        except Exception as e:
            logger.error(f"搜索电视剧时发生错误: {str(e)}")
            return []

    def search_multi(self, query: str) -> List[Dict[str, Any]]:
        """多媒体搜索（电影+电视剧）"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return []

            results = self.search.multi(query)
            if not results:
                logger.warning(f"未找到相关内容: {query}")
                return []

            media_items = []
            for item in results:
                try:
                    # 根据media_type处理不同类型的媒体
                    if getattr(item, 'media_type', None) == 'movie':
                        media_data = {
                            'id': getattr(item, 'id', None),
                            'media_type': 'movie',
                            'title': getattr(item, 'title', ''),
                            'original_title': getattr(item, 'original_title', ''),
                            'release_date': getattr(item, 'release_date', ''),
                            'overview': getattr(item, 'overview', ''),
                            'poster_path': self.get_image_url(item.get('poster_path'), self.POSTER_SIZE),
                            'vote_average': getattr(item, 'vote_average', 0.0)
                        }
                    elif getattr(item, 'media_type', None) == 'tv':
                        media_data = {
                            'id': getattr(item, 'id', None),
                            'media_type': 'tv',
                            'name': getattr(item, 'name', ''),
                            'original_name': getattr(item, 'original_name', ''),
                            'first_air_date': getattr(item, 'first_air_date', ''),
                            'overview': getattr(item, 'overview', ''),
                            'poster_path': self.get_image_url(item.get('poster_path'), self.POSTER_SIZE),
                            'vote_average': getattr(item, 'vote_average', 0.0)
                        }
                    else:
                        continue  # 跳过其他类型（如person）

                    media_items.append(media_data)
                except Exception as e:
                    logger.error(f"处理媒体数据时发生错误: {str(e)}")
                    continue

            return media_items

        except Exception as e:
            logger.error(f"多媒体搜索时发生错误: {str(e)}")
            return []

    def get_movie_details(self, movie_id: int) -> Optional[Dict[str, Any]]:
        """获取电影详细信息"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return None

            movie = self.movie.details(movie_id)
            if not movie:
                return None

            return {
                'id': getattr(movie, 'id', None),
                'title': getattr(movie, 'title', ''),
                'original_title': getattr(movie, 'original_title', ''),
                'release_date': getattr(movie, 'release_date', ''),
                'overview': getattr(movie, 'overview', ''),
                'poster_path': self.get_image_url(movie.get('poster_path'), self.POSTER_SIZE),
                'backdrop_path': self.get_image_url(movie.get('backdrop_path'), self.BACKDROP_SIZE),
                'vote_average': getattr(movie, 'vote_average', 0.0),
                'runtime': getattr(movie, 'runtime', None),
                'genres': [genre['name'] for genre in getattr(movie, 'genres', [])] if getattr(movie, 'genres',
                                                                                               None) else [],
                'budget': getattr(movie, 'budget', 0),
                'revenue': getattr(movie, 'revenue', 0),
                'status': getattr(movie, 'status', ''),
                'tagline': getattr(movie, 'tagline', ''),
                'imdb_id': getattr(movie, 'imdb_id', '')
            }
        except Exception as e:
            logger.error(f"获取电影详情时发生错误: {str(e)}")
            return None

    def get_tv_details(self, tv_id: int) -> Optional[Dict[str, Any]]:
        """获取电视剧详细信息"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return None

            tv = self.tv.details(tv_id)
            if not tv:
                return None

            return {
                'id': getattr(tv, 'id', None),
                'name': getattr(tv, 'name', ''),
                'original_name': getattr(tv, 'original_name', ''),
                'first_air_date': getattr(tv, 'first_air_date', ''),
                'last_air_date': getattr(tv, 'last_air_date', ''),
                'overview': getattr(tv, 'overview', ''),
                'poster_path': self.get_image_url(tv.get('poster_path'), self.POSTER_SIZE),
                'backdrop_path': self.get_image_url(tv.get('backdrop_path'), self.BACKDROP_SIZE),
                'vote_average': getattr(tv, 'vote_average', 0.0),
                'number_of_seasons': getattr(tv, 'number_of_seasons', 0),
                'number_of_episodes': getattr(tv, 'number_of_episodes', 0),
                'genres': [genre['name'] for genre in getattr(tv, 'genres', [])] if getattr(tv, 'genres', None) else [],
                'status': getattr(tv, 'status', ''),
                'type': getattr(tv, 'type', ''),
                'networks': [network['name'] for network in getattr(tv, 'networks', [])] if getattr(tv, 'networks',
                                                                                                    None) else [],
                'seasons': [
                    {
                        'id': season.get('id'),
                        'season_number': season.get('season_number'),
                        'name': season.get('name'),
                        'overview': season.get('overview'),
                        'poster_path': self.get_image_url(season.get('poster_path'), self.POSTER_SIZE),
                        'air_date': season.get('air_date'),
                        'episode_count': season.get('episode_count')
                    }
                    for season in getattr(tv, 'seasons', [])
                ] if getattr(tv, 'seasons', None) else []
            }
        except Exception as e:
            logger.error(f"获取电视剧详情时发生错误: {str(e)}")
            return None

    async def get_season_details(self, tv_id: int, season_num: int) -> Optional[Dict[str, Any]]:
        """
        获取季详细信息
        :param tv_id: TMDB电视剧ID
        :param season_number: 季数
        :return: 季详细信息
        """
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return None

            season = self.season.details(tv_id, season_num)
            if not season:
                return None

            return {
                'id': season.get('id'),
                'air_date': season.get('air_date'),
                'name': season.get('name'),
                'overview': season.get('overview'),
                'poster_path': self.get_image_url(season.get('poster_path'), self.POSTER_SIZE),
                'season_number': season.get('season_number'),
                'episodes': [{
                    'id': episode.get('id'),
                    'name': episode.get('name'),
                    'overview': episode.get('overview'),
                    'air_date': episode.get('air_date'),
                    'episode_number': episode.get('episode_number'),
                    'still_path': self.get_image_url(episode.get('still_path'), self.STILL_SIZE)
                } for episode in season.get('episodes', [])]
            }
        except Exception as e:
            logger.error(f"获取季详情时发生错误: {str(e)}")
            return None

    def get_trending(self, media_type: str = "all", time_window: str = "day") -> List[Dict[str, Any]]:
        """获取热门内容"""
        try:
            if not self._verify_api_key():
                logger.error("API key无效")
                return []

            # tmdbv3api没有直接的trending方法，我们使用popular代替
            if media_type == "movie":
                results = self.movie.popular()
            elif media_type == "tv":
                results = self.tv.popular()
            else:
                # 混合结果
                movie_results = self.movie.popular()[:10]
                tv_results = self.tv.popular()[:10]
                results = movie_results + tv_results

            if not results:
                return []

            trending_items = []
            for item in results:
                try:
                    # 判断是电影还是电视剧
                    if hasattr(item, 'title'):  # 电影
                        trending_data = {
                            'id': getattr(item, 'id', None),
                            'media_type': 'movie',
                            'title': getattr(item, 'title', ''),
                            'original_title': getattr(item, 'original_title', ''),
                            'release_date': getattr(item, 'release_date', ''),
                            'overview': getattr(item, 'overview', ''),
                            'poster_path': self.get_image_url(item.get('poster_path'), self.POSTER_SIZE),
                            'vote_average': getattr(item, 'vote_average', 0.0)
                        }
                    else:  # 电视剧
                        trending_data = {
                            'id': getattr(item, 'id', None),
                            'media_type': 'tv',
                            'name': getattr(item, 'name', ''),
                            'original_name': getattr(item, 'original_name', ''),
                            'first_air_date': getattr(item, 'first_air_date', ''),
                            'overview': getattr(item, 'overview', ''),
                            'poster_path': self.get_image_url(item.get('poster_path'), self.POSTER_SIZE),
                            'vote_average': getattr(item, 'vote_average', 0.0)
                        }

                    trending_items.append(trending_data)
                except Exception as e:
                    logger.error(f"处理热门内容数据时发生错误: {str(e)}")
                    continue

            return trending_items

        except Exception as e:
            logger.error(f"获取热门内容时发生错误: {str(e)}")
            return []

    def get_movie_full_info(self, search_name: str, year: Optional[int] = None, calculate_similarity: bool = True) -> \
            Optional[Dict[str, Any]]:
        """通过名称搜索电影并获取完整信息"""
        try:
            # 1. 搜索电影
            search_results = self.search_movie(search_name, year)
            if not search_results:
                logger.warning(f"未找到相关电影: {search_name}")
                return None

            if not calculate_similarity:
                # 返回第一个结果的详细信息
                movie_details = self.get_movie_details(search_results[0]['id'])
                return movie_details

            # 2. 找到最匹配的结果
            best_match = None
            highest_similarity = 0

            for result in search_results:
                # 分别计算原名和译名的相似度
                title_similarity = self._calculate_similarity(search_name, result['title'])
                original_title_similarity = self._calculate_similarity(search_name, result['original_title'])
                # 取较高的相似度
                similarity = max(title_similarity, original_title_similarity)

                if similarity > highest_similarity:
                    highest_similarity = similarity
                    best_match = result

            if not best_match:
                logger.warning(f"未找到匹配的电影: {search_name}")
                return None

            # 3. 获取电影详细信息
            movie_details = self.get_movie_details(best_match['id'])
            if not movie_details:
                logger.error(f"获取电影详情失败: {search_name}")
                return None

            # 4. 添加相似度信息
            movie_details['similarity'] = highest_similarity
            return movie_details

        except Exception as e:
            logger.error(f"获取电影完整信息时发生错误: {str(e)}")
            return None

    def get_tv_full_info(self, search_name: str, year: Optional[int] = None, calculate_similarity: bool = True) -> \
            Optional[Dict[str, Any]]:
        """通过名称搜索电视剧并获取完整信息"""
        try:
            # 1. 搜索电视剧
            search_results = self.search_tv(search_name, year)
            if not search_results:
                logger.warning(f"未找到相关电视剧: {search_name}")
                return None

            if not calculate_similarity:
                # 返回第一个结果的详细信息
                tv_details = self.get_tv_details(search_results[0]['id'])
                return tv_details

            # 2. 找到最匹配的结果
            best_match = None
            highest_similarity = 0

            for result in search_results:
                # 分别计算原名和译名的相似度
                name_similarity = self._calculate_similarity(search_name, result['name'])
                original_name_similarity = self._calculate_similarity(search_name, result['original_name'])
                # 取较高的相似度
                similarity = max(name_similarity, original_name_similarity)

                if similarity > highest_similarity:
                    highest_similarity = similarity
                    best_match = result

            if not best_match:
                logger.warning(f"未找到匹配的电视剧: {search_name}")
                return None

            # 3. 获取电视剧详细信息
            tv_details = self.get_tv_details(best_match['id'])
            if not tv_details:
                logger.error(f"获取电视剧详情失败: {search_name}")
                return None

            # 4. 添加相似度信息
            tv_details['similarity'] = highest_similarity
            return tv_details

        except Exception as e:
            logger.error(f"获取电视剧完整信息时发生错误: {str(e)}")
            return None

    def get_image_url(self, path: str, size: str = "w500") -> str:
        """获取图片完整URL"""
        if not path:
            return ""

        # 使用tmdbv3api的图片基础URL
        base_url = "https://image.tmdb.org/t/p/"
        return f"{base_url}{size}{path}"

    def format_movie_info(self, movie_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化电影信息（兼容旧API）"""
        return {
            "id": movie_data.get("id"),
            "title": movie_data.get("title"),
            "original_title": movie_data.get("original_title"),
            "overview": movie_data.get("overview"),
            "release_date": movie_data.get("release_date"),
            "poster_path": self.get_image_url(movie_data.get("poster_path"), self.POSTER_SIZE),
            "backdrop_path": self.get_image_url(movie_data.get("backdrop_path"), self.BACKDROP_SIZE),
            "vote_average": movie_data.get("vote_average"),
            "vote_count": movie_data.get("vote_count"),
            "popularity": movie_data.get("popularity"),
            "genre_ids": movie_data.get("genre_ids", []),
            "adult": movie_data.get("adult", False),
            "video": movie_data.get("video", False),
            "original_language": movie_data.get("original_language"),
        }

    def format_tv_info(self, tv_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化电视剧信息（兼容旧API）"""
        return {
            "id": tv_data.get("id"),
            "name": tv_data.get("name"),
            "original_name": tv_data.get("original_name"),
            "overview": tv_data.get("overview"),
            "first_air_date": tv_data.get("first_air_date"),
            "poster_path": self.get_image_url(tv_data.get("poster_path"), self.POSTER_SIZE),
            "backdrop_path": self.get_image_url(tv_data.get("backdrop_path"), self.BACKDROP_SIZE),
            "vote_average": tv_data.get("vote_average"),
            "vote_count": tv_data.get("vote_count"),
            "popularity": tv_data.get("popularity"),
            "genre_ids": tv_data.get("genre_ids", []),
            "origin_country": tv_data.get("origin_country", []),
            "original_language": tv_data.get("original_language"),
        }

    def check_tmdb_availability(self) -> Dict[str, Any]:
        """检查TMDB服务可用性"""
        try:
            if not self.is_configured:
                return {
                    "available": False,
                    "configured": False,
                    "error": "TMDB API密钥未配置"
                }

            # 尝试获取配置信息来测试API连接
            from tmdbv3api import Configuration
            config = Configuration()
            config_data = config.info()

            # 确保返回的是可序列化的数据
            images_config = config_data.get("images", {}) if hasattr(config_data, 'get') else {}

            return {
                "available": True,
                "configured": True,
                "api_key_configured": bool(self.tmdb.api_key),
                "language": self.tmdb.language,
                "base_url": str(images_config.get("secure_base_url", "")),
                "poster_sizes": list(images_config.get("poster_sizes", [])),
                "backdrop_sizes": list(images_config.get("backdrop_sizes", []))
            }

        except Exception as e:
            logger.error(f"TMDB可用性检查失败: {e}")
            return {
                "available": False,
                "configured": self.is_configured,
                "error": str(e)
            }


# 向后兼容
tmdb_service = TMDBService()
