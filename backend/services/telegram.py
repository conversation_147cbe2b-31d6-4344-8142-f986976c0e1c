"""
Telegram 核心服务
负责Telegram Bot交互、消息发送、频道搜索等功能
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Any

import httpx
from loguru import logger as logger

from backend.utils import Singleton
from backend.utils.config import settings


class TelegramError(Exception):
    """Telegram API错误"""
    pass


class TelegramService(Singleton):
    """Telegram服务类"""

    def __init__(self):
        self.bot_token = settings.telegram_bot_token
        self.chat_id = settings.telegram_chat_id
        self.enabled = settings.telegram_enabled
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}" if self.bot_token else ""

        if not self.bot_token and self.enabled:
            logger.warning("Telegram Bot Token未配置，Telegram功能将不可用")

    async def _make_request(
            self,
            method: str,
            data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """发起Telegram API请求"""
        if not self.bot_token:
            raise TelegramError("Telegram Bot Token未配置")

        url = f"{self.base_url}/{method}"

        try:
            async with httpx.AsyncClient() as client:
                if data:
                    response = await client.post(url, json=data, timeout=30.0)
                else:
                    response = await client.get(url, timeout=30.0)

                response.raise_for_status()
                result = response.json()

                if not result.get("ok"):
                    raise TelegramError(f"Telegram API错误: {result.get('description')}")

                return result.get("result", {})

        except httpx.HTTPStatusError as e:
            logger.error(f"Telegram API请求失败: {e.response.status_code} - {e.response.text}")
            raise TelegramError(f"Telegram API请求失败: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Telegram API网络错误: {str(e)}")
            raise TelegramError(f"Telegram API网络错误: {str(e)}")

    async def send_message(
            self,
            text: str,
            chat_id: Optional[str] = None,
            parse_mode: str = "HTML",
            disable_web_page_preview: bool = True
    ) -> Dict[str, Any]:
        """发送消息"""
        if not self.enabled:
            logger.debug("Telegram功能未启用，跳过消息发送")
            return {}

        target_chat_id = chat_id or self.chat_id
        if not target_chat_id:
            raise TelegramError("未指定聊天ID")

        data = {
            "chat_id": target_chat_id,
            "text": text,
            "parse_mode": parse_mode,
            "disable_web_page_preview": disable_web_page_preview
        }

        return await self._make_request("sendMessage", data)

    async def send_photo(
            self,
            photo: str,
            caption: Optional[str] = None,
            chat_id: Optional[str] = None,
            parse_mode: str = "HTML"
    ) -> Dict[str, Any]:
        """发送图片"""
        if not self.enabled:
            logger.debug("Telegram功能未启用，跳过图片发送")
            return {}

        target_chat_id = chat_id or self.chat_id
        if not target_chat_id:
            raise TelegramError("未指定聊天ID")

        data = {
            "chat_id": target_chat_id,
            "photo": photo,
            "parse_mode": parse_mode
        }

        if caption:
            data["caption"] = caption

        return await self._make_request("sendPhoto", data)

    async def get_me(self) -> Dict[str, Any]:
        """获取Bot信息"""
        return await self._make_request("getMe")

    async def get_chat(self, chat_id: str) -> Dict[str, Any]:
        """获取聊天信息"""
        data = {"chat_id": chat_id}
        return await self._make_request("getChat", data)

    async def search_channel_messages(
            self,
            channel_username: str,
            query: str,
            limit: int = 50
    ) -> List[Dict[str, Any]]:
        """搜索频道消息（需要频道公开且Bot有权限）"""
        # 注意：这个功能需要特殊权限，通常用于公开频道
        # 实际实现可能需要使用Telegram Client API而不是Bot API

        logger.warning("频道消息搜索功能需要特殊实现，当前返回空结果")
        return []

    async def parse_magnet_from_message(self, message_text: str) -> List[str]:
        """从消息中解析磁力链接"""
        magnet_pattern = r'magnet:\?xt=urn:btih:[a-fA-F0-9]{40}[^\s]*'
        magnets = re.findall(magnet_pattern, message_text)
        return magnets

    async def parse_torrent_info_from_message(self, message_text: str) -> Dict[str, Any]:
        """从消息中解析种子信息"""
        info = {
            "title": "",
            "size": "",
            "seeders": 0,
            "leechers": 0,
            "magnets": []
        }

        # 解析标题（通常在第一行或加粗文本中）
        title_patterns = [
            r'<b>(.*?)</b>',  # HTML加粗
            r'\*\*(.*?)\*\*',  # Markdown加粗
            r'^([^\n]+)',  # 第一行
        ]

        for pattern in title_patterns:
            match = re.search(pattern, message_text, re.MULTILINE)
            if match:
                info["title"] = match.group(1).strip()
                break

        # 解析大小
        size_pattern = r'(?:大小|Size|文件大小)[:：]\s*([0-9.]+\s*[KMGT]?B)'
        size_match = re.search(size_pattern, message_text, re.IGNORECASE)
        if size_match:
            info["size"] = size_match.group(1)

        # 解析做种数
        seeders_pattern = r'(?:做种|Seeders?)[:：]\s*(\d+)'
        seeders_match = re.search(seeders_pattern, message_text, re.IGNORECASE)
        if seeders_match:
            info["seeders"] = int(seeders_match.group(1))

        # 解析下载数
        leechers_pattern = r'(?:下载|Leechers?)[:：]\s*(\d+)'
        leechers_match = re.search(leechers_pattern, message_text, re.IGNORECASE)
        if leechers_match:
            info["leechers"] = int(leechers_match.group(1))

        # 解析磁力链接
        info["magnets"] = await self.parse_magnet_from_message(message_text)

        return info

    async def send_download_notification(
            self,
            title: str,
            status: str,
            progress: Optional[float] = None,
            speed: Optional[str] = None,
            eta: Optional[str] = None
    ) -> None:
        """发送下载通知"""
        if not self.enabled:
            return

        status_emoji = {
            "started": "🚀",
            "downloading": "⬇️",
            "completed": "✅",
            "failed": "❌",
            "paused": "⏸️"
        }

        emoji = status_emoji.get(status, "📥")

        message = f"{emoji} <b>下载状态更新</b>\n\n"
        message += f"📺 <b>标题:</b> {title}\n"
        message += f"📊 <b>状态:</b> {status}\n"

        if progress is not None:
            message += f"📈 <b>进度:</b> {progress:.1f}%\n"

        if speed:
            message += f"⚡ <b>速度:</b> {speed}\n"

        if eta:
            message += f"⏰ <b>预计完成:</b> {eta}\n"

        message += f"\n🕐 <b>时间:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        try:
            await self.send_message(message)
            logger.info(f"已发送下载通知: {title} - {status}")
        except Exception as e:
            logger.error(f"发送下载通知失败: {e}")

    async def send_subscription_notification(
            self,
            title: str,
            episode_info: str,
            action: str = "new_episode"
    ) -> None:
        """发送订阅通知"""
        if not self.enabled:
            return

        action_emoji = {
            "new_episode": "🆕",
            "season_complete": "🎉",
            "subscription_added": "➕",
            "subscription_updated": "🔄"
        }

        emoji = action_emoji.get(action, "📺")

        message = f"{emoji} <b>订阅更新</b>\n\n"
        message += f"📺 <b>剧集:</b> {title}\n"
        message += f"📝 <b>详情:</b> {episode_info}\n"
        message += f"\n🕐 <b>时间:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        try:
            await self.send_message(message)
            logger.info(f"已发送订阅通知: {title} - {episode_info}")
        except Exception as e:
            logger.error(f"发送订阅通知失败: {e}")

    async def test_connection(self) -> bool:
        """测试Telegram连接"""
        if not self.bot_token:
            return False

        try:
            bot_info = await self.get_me()
            logger.info(f"Telegram Bot连接成功: {bot_info.get('username')}")
            return True
        except Exception as e:
            logger.error(f"Telegram Bot连接失败: {e}")
            return False


# 全局Telegram服务实例
telegram_service = TelegramService()
