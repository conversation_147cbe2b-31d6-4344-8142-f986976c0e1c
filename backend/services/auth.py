"""
简化认证服务
只保留核心功能，减少复杂性
"""

from datetime import timedelta
from typing import Optional, Dict, Any

from loguru import logger as logger
from sqlalchemy.orm import Session

from backend.database.models import User
from backend.utils import hash_password, verify_password, generate_token, verify_token, Singleton, settings
from backend.utils.datetime import now_utc


class AuthError(Exception):
    """认证错误基类"""
    pass


class AuthenticationError(AuthError):
    """认证失败错误（用户名密码错误）"""
    pass


class AuthorizationError(AuthError):
    """授权失败错误（token无效等）"""
    pass

class UserInactiveError(AuthError):
    """用户已禁用错误"""
    pass


class PasswordError(AuthError):
    """密码相关错误"""
    pass


class TokenError(AuthError):
    """token相关错误"""
    pass


class AuthService(Singleton):
    """认证服务类"""

    def authenticate_user(self, db: Session, username: str, password: str) -> User:
        """验证用户"""
        user = db.query(User).filter(User.username == username, User.is_active == True).first()
        if not user or not verify_password(password, user.hashed_password):
            raise AuthenticationError("用户名或密码错误")
        return user

    def invalidate_previous_tokens(self, db: Session, user: User) -> None:
        """使用户之前的所有token失效"""
        try:
            user.logout_time = now_utc()
            db.commit()
        except Exception as e:
            logger.error(f"使用户 {user.username} 旧token失效时出错: {e}")
            db.rollback()
            raise AuthError("token失效操作失败")

    def create_access_token(self, user: User) -> str:
        """创建访问令牌"""
        data = {
            "sub": user.username,
            "user_id": user.id,
            "is_admin": True,  # 所有用户都是管理员
            "iat": int(now_utc().timestamp())  # 添加签发时间
        }
        expires_delta = timedelta(minutes=settings.security_jwt_expire_minutes)
        return generate_token(data, expires_delta)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        return verify_token(token)

    def get_user_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username, User.is_active == True).first()

    def change_password(self, db: Session, user: User, old_password: str, new_password: str) -> bool:
        """修改密码"""
        if not verify_password(old_password, user.hashed_password):
            raise PasswordError("原密码错误")

        try:
            user.hashed_password = hash_password(new_password)
            db.commit()
            logger.info(f"用户 {user.username} 修改密码成功")
            return True
        except Exception as e:
            logger.error(f"用户 {user.username} 修改密码失败: {e}")
            db.rollback()
            raise AuthError("密码修改失败")

    def logout_user(self, db: Session, user: User) -> None:
        """用户登出，设置登出时间使token失效"""
        try:
            user.logout_time = now_utc()
            db.commit()
            logger.info(f"用户 {user.username} 登出成功")
        except Exception as e:
            logger.error(f"用户 {user.username} 登出失败: {e}")
            db.rollback()
            raise AuthError("登出失败")

# 全局认证服务实例
auth_service = AuthService()
