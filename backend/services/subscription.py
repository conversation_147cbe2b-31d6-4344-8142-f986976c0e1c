"""
Subscription service for managing media subscriptions
"""
from typing import Optional, Dict, Any

from loguru import logger as logger
from sqlalchemy.orm import Session

from backend.database.models import Subscription, Download
from . import tmdb_service
from ..core.downloaders.qbittorrent import qb_downloader
from ..core.notifications.telegram import telegram_notification
from ..core.subscription.mikan import mikan_parser
from ..utils import Singleton


class SubscriptionService(Singleton):
    """
    Service for managing subscriptions and checking for new content
    """

    def __init__(self, db: Session):
        self.db = db

    async def check_subscription_updates(self, subscription_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Check for updates in subscriptions
        
        Args:
            subscription_id: Optional specific subscription to check
            
        Returns:
            Summary of updates found
        """
        query = self.db.query(Subscription).filter(Subscription.status == "active")

        if subscription_id:
            query = query.filter(Subscription.id == subscription_id)

        subscriptions = query.all()

        total_new_episodes = 0
        updated_subscriptions = []

        for subscription in subscriptions:
            try:
                new_episodes = await self._check_single_subscription(subscription)
                if new_episodes > 0:
                    total_new_episodes += new_episodes
                    updated_subscriptions.append({
                        "id": subscription.id,
                        "name": subscription.name,
                        "new_episodes": new_episodes
                    })

                    # Send notification
                    await telegram_notification.send_subscription_update(
                        subscription.name,
                        new_episodes
                    )

            except Exception as e:
                logger.error(f"Error checking subscription {subscription.id}: {e}")

        return {
            "total_subscriptions_checked": len(subscriptions),
            "total_new_episodes": total_new_episodes,
            "updated_subscriptions": updated_subscriptions
        }

    async def _check_single_subscription(self, subscription: Subscription) -> int:
        """
        Check a single subscription for new content
        
        Args:
            subscription: Subscription to check
            
        Returns:
            Number of new episodes found
        """
        new_episodes = 0

        if subscription.type == "mikan" and subscription.rss_url:
            new_episodes = await self._check_mikan_subscription(subscription)
        elif subscription.type == "tmdb" and subscription.tmdb_id:
            new_episodes = await self._check_tmdb_subscription(subscription)

        # Update last checked time
        from sqlalchemy.sql import func
        subscription.last_checked = func.now()
        self.db.commit()

        return new_episodes

    async def _check_mikan_subscription(self, subscription: Subscription) -> int:
        """
        Check Mikan RSS subscription for new episodes
        """
        try:
            async with mikan_parser as parser:
                episodes = await parser.parse_rss_feed(subscription.rss_url)

            new_episodes = 0

            for episode in episodes:
                # Check if we already have this episode
                existing_download = self.db.query(Download).filter(
                    Download.subscription_id == subscription.id,
                    Download.name == episode["title"]
                ).first()

                if existing_download:
                    continue

                # Check if episode matches subscription filters
                if not self._matches_subscription_filters(episode, subscription):
                    continue

                # Add download if auto_download is enabled
                if subscription.auto_download and episode.get("magnet_link"):
                    await self._add_download_from_episode(episode, subscription)
                    new_episodes += 1

            return new_episodes

        except Exception as e:
            logger.error(f"Error checking Mikan subscription {subscription.id}: {e}")
            return 0

    async def _check_tmdb_subscription(self, subscription: Subscription) -> int:
        """
        Check TMDB subscription for new episodes/seasons
        """
        try:
            if subscription.type == "tv":
                tv_details = await tmdb_service.get_tv_details(subscription.tmdb_id)
                if not tv_details:
                    return 0

                # Check for new seasons
                current_season = subscription.season_number or 1
                latest_season = tv_details.get("number_of_seasons", 1)

                if latest_season > current_season:
                    # New season available
                    subscription.season_number = latest_season
                    self.db.commit()
                    return 1

            return 0

        except Exception as e:
            logger.error(f"Error checking TMDB subscription {subscription.id}: {e}")
            return 0

    def _matches_subscription_filters(self, episode: Dict[str, Any], subscription: Subscription) -> bool:
        """
        Check if episode matches subscription filters
        """
        title = episode.get("title", "").lower()

        # Check keywords
        if subscription.keywords:
            keyword_match = any(keyword.lower() in title for keyword in subscription.keywords)
            if not keyword_match:
                return False

        # Check exclude keywords
        if subscription.exclude_keywords:
            exclude_match = any(keyword.lower() in title for keyword in subscription.exclude_keywords)
            if exclude_match:
                return False

        # Check quality filter
        if subscription.quality_filter:
            quality = episode.get("quality", "")
            if subscription.quality_filter.lower() not in quality.lower():
                return False

        return True

    async def _add_download_from_episode(self, episode: Dict[str, Any], subscription: Subscription) -> Optional[
        Download]:
        """
        Add download from episode information
        """
        try:
            # Create download record
            download = Download(
                subscription_id=subscription.id,
                name=episode["title"],
                original_name=episode["title"],
                type="torrent",
                status="pending",
                download_url=episode["magnet_link"],
                auto_rename=subscription.auto_rename,
                auto_upload=subscription.auto_upload,
            )

            self.db.add(download)
            self.db.commit()
            self.db.refresh(download)

            # Add to download client
            client_id = await qb_downloader.add_download(
                episode["magnet_link"],
                category="MediPaka"
            )

            if client_id:
                download.client_type = "qbittorrent"
                download.client_id = client_id
                download.status = "downloading"
                self.db.commit()

                # Send notification
                await telegram_notification.send_download_started(download.name)

                logger.info(f"Added download: {download.name}")
                return download
            else:
                download.status = "failed"
                download.error_message = "Failed to add to download client"
                self.db.commit()

                await telegram_notification.send_download_failed(
                    download.name,
                    "Failed to add to download client"
                )

                return None

        except Exception as e:
            logger.error(f"Error adding download from episode: {e}")
            return None

    async def add_subscription_from_tmdb(
            self,
            tmdb_id: int,
            media_type: str,
            **kwargs
    ) -> Optional[Subscription]:
        """
        Add subscription from TMDB ID
        """
        try:
            # Get TMDB details
            if media_type == "movie":
                details = await tmdb_service.get_movie_details(tmdb_id)
                title = details.get("title")
            else:
                details = await tmdb_service.get_tv_details(tmdb_id)
                title = details.get("name")

            if not details:
                return None

            # Create subscription
            subscription = Subscription(
                name=title,
                type=media_type,
                tmdb_id=tmdb_id,
                description=details.get("overview"),
                poster_url=tmdb_service.get_image_url(details.get("poster_path")),
                year=int(details.get("release_date", details.get("first_air_date", ""))[:4]) if details.get(
                    "release_date") or details.get("first_air_date") else None,
                **kwargs
            )

            self.db.add(subscription)
            self.db.commit()
            self.db.refresh(subscription)

            logger.info(f"Added TMDB subscription: {title}")
            return subscription

        except Exception as e:
            logger.error(f"Error adding TMDB subscription: {e}")
            return None
