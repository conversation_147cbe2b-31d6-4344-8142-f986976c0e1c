"""
服务模块
包含所有业务逻辑服务，负责具体的功能实现
"""

# 认证服务
from .auth import auth_service, AuthService, AuthError
# 豆瓣服务
from .douban import douban_service, DoubanService, DoubanError
# 搜索服务
from .search import search_service, SearchService, SearchResult, SearchEngine
# Telegram服务
from .telegram import telegram_service, TelegramService, TelegramError
# TMDB服务
from .tmdb import tmdb_service, TMDBService, TMDBError

# 订阅服务（暂时注释，避免循环导入）
# from .subscription import SubscriptionService

__all__ = [
    # TMDB相关
    "tmdb_service",
    "TMDBError",

    # 豆瓣相关
    "douban_service",
    "DoubanError",

    # Telegram相关
    "telegram_service",
    "TelegramError",

    # 搜索相关
    "search_service",
    "SearchResult",
    "SearchEngine",

    # 认证相关
    "auth_service",
    "AuthError",

    # 订阅相关（暂时注释）
    # "SubscriptionService",
]


def get_service_status():
    """获取所有服务状态"""
    return {
        "tmdb": {
            "enabled": bool(tmdb_service.api_key),
            "service": "TMDBService"
        },
        "telegram": {
            "enabled": telegram_service.enabled,
            "service": "TelegramService"
        },
        "search": {
            "engines": len(search_service.engines),
            "service": "SearchService"
        },
        "auth": {
            "enabled": True,
            "service": "AuthService"
        }
    }
