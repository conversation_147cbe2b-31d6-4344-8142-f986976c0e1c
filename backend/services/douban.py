"""
豆瓣服务
提供豆瓣电影、电视剧信息查询功能
优化版本：使用HTTP客户端工具类，支持缓存、重试、限流
"""

import re
from typing import Any, List, Optional, Dict

from loguru import logger as logger

from backend.utils import Singleton
from backend.utils.httpclient import HttpClient, HttpConfig, HttpClientError, HttpNotFoundError, HttpRateLimitError


class DoubanError(Exception):
    """豆瓣API错误"""
    pass


class DoubanRateLimitError(DoubanError):
    """豆瓣API限流错误"""
    pass


class DoubanNotFoundError(DoubanError):
    """豆瓣资源未找到错误"""
    pass


class DoubanService(Singleton):
    """豆瓣服务类"""

    def __init__(self):
        # 基础配置
        self.base_url = "https://m.douban.com/rexxar/api/v2"

        # 配置HTTP客户端
        self.http_config = HttpConfig(
            timeout=30.0,
            max_retries=3,
            retry_delay=1.0,
            verify_ssl=False,
            rate_limit_enabled=True,
            requests_per_second=3.0,  # 豆瓣限流较严格
            burst_size=5,
            cache_enabled=True,
            cache_size=256,
            cache_ttl=300,
            default_headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Origin": "https://movie.douban.com",
                "Referer": "https://movie.douban.com/",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-site",
                "Priority": "u=1, i"
            }
        )

        # 创建HTTP客户端
        self.http_client = HttpClient(self.http_config)

        # 编译正则表达式
        self.name_pattern = re.compile(r'^([^（]+)(?:[（]([^）]+)[）])?$')

        logger.info("豆瓣服务已初始化")

    async def _get_json(self, endpoint: str, params: Optional[Dict] = None, use_cache: bool = True, **kwargs) -> Dict[
        str, Any]:
        """
        GET请求并返回JSON数据
        使用HTTP客户端工具类
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            return await self.http_client.get_json(url, params=params, use_cache=use_cache, **kwargs)
        except HttpNotFoundError as e:
            raise DoubanNotFoundError(str(e))
        except HttpRateLimitError as e:
            raise DoubanRateLimitError(str(e))
        except HttpClientError as e:
            raise DoubanError(str(e))

    def clear_cache(self):
        """清除所有缓存"""
        self.http_client.clear_cache()
        logger.info("豆瓣服务缓存已清除")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self.http_client.get_cache_info()

    def get_rate_limit_info(self) -> Dict[str, Any]:
        """获取限流信息"""
        return self.http_client.get_rate_limit_info()

    async def close(self):
        """关闭HTTP客户端"""
        await self.http_client.close()

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        return {
            "service_name": "豆瓣服务",
            "base_url": self.base_url,
            "http_client_config": {
                "timeout": self.http_config.timeout,
                "max_retries": self.http_config.max_retries,
                "rate_limit_enabled": self.http_config.rate_limit_enabled,
                "requests_per_second": self.http_config.requests_per_second,
                "cache_enabled": self.http_config.cache_enabled,
                "cache_size": self.http_config.cache_size,
                "cache_ttl": self.http_config.cache_ttl
            },
            "cache_info": self.get_cache_info(),
            "rate_limit_info": self.get_rate_limit_info(),
            "supported_features": [
                "热门列表获取",
                "搜索功能",
                "详细信息获取",
                "缓存优化",
                "限流保护",
                "重试机制"
            ]
        }

    def _get_cover_url(self, item: dict) -> str:
        """获取封面图片URL"""
        # 优先使用高清图片
        if "pic" in item and item["pic"].get("normal"):
            return item["pic"]["normal"]
        # 备选使用封面图片
        if "cover" in item and item["cover"].get("url"):
            return item["cover"]["url"]
        return ""

    def _convert_to_subject(self, item: dict) -> Dict[str, Any]:
        """将API返回的数据转换为标准格式"""
        return {
            "id": str(item.get("id")),
            "title": item.get("title", ""),
            "rate": str(item.get("rating", {}).get("value", "")),
            "cover": self._get_cover_url(item),
            "url": f"https://movie.douban.com/subject/{item.get('id')}/",
            "cover_x": item.get("cover", {}).get("width"),
            "cover_y": item.get("cover", {}).get("height"),
            "is_new": item.get("is_new", False),
            "type": item.get("type"),
            "episodes_info": item.get("episodes_info"),
            "card_subtitle": item.get("card_subtitle"),
            "description": item.get("description"),
            "honor_infos": item.get("honor_infos"),
            "year": item.get("year"),
            "release_date": item.get("release_date"),
            "directors": [p.get("name") for p in item.get("directors", []) if p.get("name")],
            "actors": [p.get("name") for p in item.get("actors", []) if p.get("name")],
            "regions": item.get("regions", [])
        }

    async def _get_hot_list_async(
            self,
            type: str = "movie",
            category: Optional[str] = None,
            page: int = 1,
            count: int = 20
    ) -> List[Dict[str, Any]]:
        """获取热门列表的异步版本"""
        try:
            start = (page - 1) * count
            endpoint = f"subject/recent_hot/{type}"
            params: Dict[str, Any] = {
                "start": start,
                "limit": count
            }

            if type == "tv":
                params["type"] = "tv"
                if category:
                    params["type"] = category
                    params["category"] = category

            # 使用HTTP客户端工具类
            data = await self._get_json(endpoint, params=params)

            if isinstance(data, dict) and "items" in data:
                results = [self._convert_to_subject(item) for item in data["items"]]
                logger.info(f"获取热门{type}列表成功，共 {len(results)} 条")
                return results

            logger.warning(f"热门{type}列表返回数据格式异常")
            return []

        except DoubanError:
            raise
        except Exception as e:
            logger.error(f"获取热门{type}列表失败: {e}")
            raise DoubanError(f"获取热门{type}列表失败: {str(e)}")

    async def get_hot_list(
            self,
            type: str = "movie",
            category: Optional[str] = None,
            page: int = 1,
            count: int = 20
    ) -> List[Dict[str, Any]]:
        """
        获取热门电影、电视剧或综艺列表
        使用HTTP客户端工具类，支持缓存和限流
        """
        try:
            return await self._get_hot_list_async(type, category, page, count)

        except Exception as e:
            logger.error(f"获取豆瓣热门列表失败: {e}")
            raise DoubanError(f"获取豆瓣热门列表失败: {str(e)}")

    async def _search_async(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索的异步版本"""
        try:
            if not keyword:
                return []

            # 使用移动版API进行搜索，更稳定
            params = {
                "q": keyword,
                "count": min(limit, 50),
                "start": 0
            }

            # 使用HTTP客户端工具类
            data = await self._get_json("search/movie", params=params)

            if not isinstance(data, dict) or "items" not in data:
                logger.warning(f"搜索返回数据格式异常: {keyword}")
                return []

            results = []
            for item in data["items"][:limit]:
                # 解析基本信息
                result = {
                    "id": str(item.get("id", "")),
                    "title": item.get("title", ""),
                    "original_title": item.get("original_title", ""),
                    "year": item.get("year", ""),
                    "type": item.get("type", "movie"),
                    "rating": item.get("rating", {}),
                    "cover": item.get("cover", ""),
                    "url": item.get("url", ""),
                    "directors": [d.get("name", "") for d in item.get("directors", [])],
                    "actors": [a.get("name", "") for a in item.get("actors", [])],
                    "genres": item.get("genres", [])
                }
                results.append(result)

            logger.info(f"搜索成功: {keyword}, 找到 {len(results)} 个结果")
            return results

        except DoubanError:
            raise
        except Exception as e:
            logger.error(f"搜索失败: {keyword}, 错误: {e}")
            raise DoubanError(f"搜索失败: {str(e)}")

    async def search(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索电影和电视剧
        使用HTTP客户端工具类，支持缓存和限流
        """
        try:
            return await self._search_async(keyword, limit)
        except DoubanError:
            raise
        except Exception as e:
            logger.error(f"搜索失败: {keyword}, 错误: {e}")
            raise DoubanError(f"搜索失败: {str(e)}")

            # 尝试使用不同的解析器
            try:
                soup = BeautifulSoup(html_content, 'lxml')
            except Exception:
                try:
                    soup = BeautifulSoup(html_content, 'html.parser')
                except Exception as e:
                    logger.error(f"HTML解析失败: {e}")
                    raise DoubanError(f"HTML解析失败: {str(e)}")
            results = []

            # 查找搜索结果
            for item in soup.select("div.result-list div.result")[:limit]:
                # 获取标题和ID
                title_elem = item.select_one("div.title a")
                if not title_elem:
                    continue

                title = title_elem.get_text(strip=True)
                onclick = title_elem.get("onclick", "")
                sid = ""
                if onclick and isinstance(onclick, str) and "sid:" in onclick:
                    sid = onclick.split("sid:")[1].split(",")[0].strip()

                # 获取评分
                rating = item.select_one("div.rating-info span.rating_nums")
                rate = rating.get_text(strip=True) if rating else "0"

                # 获取图片
                img_elem = item.select_one("a.nbg img")
                img_url = str(img_elem.get("src", "")) if img_elem else ""

                # 获取年份
                subject_info = item.select_one("div.rating-info span.subject-cast")
                year = ""
                if subject_info:
                    text = subject_info.get_text(strip=True)
                    year_match = re.search(r'\d{4}', text)
                    if year_match:
                        year = year_match.group(0)

                # 判断类型（电影/电视剧）
                type_elem = item.select_one("div.title span")
                type_text = type_elem.get_text(strip=True) if type_elem else ""
                if type_text not in ["[电影]", "[电视剧]"]:
                    continue

                # 获取演员和导演信息
                cast_info = subject_info.get_text(strip=True) if subject_info else ""
                directors = []
                actors = []
                if "导演:" in cast_info:
                    director_part = cast_info.split("导演:")[1].split("主演:")[0]
                    directors = [d.strip() for d in director_part.split("/") if d.strip()]
                if "主演:" in cast_info:
                    actor_part = cast_info.split("主演:")[1].split("/")[0]
                    actors = [a.strip() for a in actor_part.split(" ") if a.strip()]

                subject = {
                    "id": sid,
                    "title": title,
                    "rate": rate,
                    "cover": img_url,
                    "url": f"https://movie.douban.com/subject/{sid}/",
                    "type": "movie" if type_text == "[电影]" else "tv",
                    "year": year,
                    "cover_x": 0,
                    "cover_y": 0,
                    "directors": directors,
                    "actors": actors,
                    "description": "",
                    "regions": [],
                    "card_subtitle": "",
                    "honor_infos": []
                }
                results.append(subject)
            return results

        except Exception as e:
            logger.error(f"搜索豆瓣失败: {e}")
            raise DoubanError(f"搜索豆瓣失败: {str(e)}")

    async def _get_movie_info_async(self, sid: str) -> Dict[str, Any]:
        """获取电影详细信息的异步版本"""
        try:
            endpoint = f"movie/{sid}"

            # 使用HTTP客户端工具类
            data = await self._get_json(endpoint)

            if not isinstance(data, dict):
                raise DoubanError("电影信息不存在")

            # 解析基本信息
            movie_info = {
                "sid": str(data.get("id")),
                "subtype": data.get("subtype", ""),
                "name": data.get("title", ""),
                "original_name": data.get("original_title", ""),
                "rating": str(data.get("rating", {}).get("value", "0")),
                "img": data.get("pic", {}).get("normal", ""),
                "year": str(data.get("year", "")),
                "episodes_count": data.get("episodes_count", 0),
                "intro": data.get("intro", ""),
                "director": [p.get("name", "") for p in data.get("directors", [])],
                "writer": [p.get("name", "") for p in data.get("writers", [])],
                "actor": [p.get("name", "") for p in data.get("actors", [])],
                "genre": data.get("genres", []),
                "site": data.get("website", ""),
                "country": data.get("countries", []),
                "language": data.get("languages", []),
                "screen": data.get("pubdate", []),
                "duration": data.get("durations", [""])[0] if data.get("durations") else "",
                "subname": data.get("aka", []),
                "imdb": data.get("imdb", ""),
                "celebrities": []  # 暂时不获取演职人员详细信息
            }

            logger.info(f"获取电影详情成功: {movie_info.get('name', 'N/A')} ({sid})")
            return movie_info

        except DoubanError:
            raise
        except Exception as e:
            logger.error(f"获取电影详情失败: {sid}, 错误: {e}")
            raise DoubanError(f"获取电影详情失败: {str(e)}")

    async def get_movie_info(self, sid: str) -> Dict[str, Any]:
        """
        获取电影详细信息
        使用HTTP客户端工具类，支持缓存和限流
        """
        try:
            return await self._get_movie_info_async(sid)
        except DoubanError:
            raise
        except Exception as e:
            logger.error(f"获取电影详情失败: {e}")
            raise DoubanError(f"获取电影详情失败: {str(e)}")


# 向后兼容
douban_service = DoubanService()
