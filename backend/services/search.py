"""
搜索引擎核心服务
整合多种搜索源：BT站点、Telegram频道、TMDB等
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any

from loguru import logger  as logger

from backend.services.telegram import telegram_service
from backend.services.tmdb import tmdb_service
from backend.utils import Singleton


class SearchResult(Singleton):
    """搜索结果数据类"""

    def __init__(
            self,
            title: str,
            url: str,
            size: str = "",
            seeders: int = 0,
            leechers: int = 0,
            source: str = "",
            quality: str = "",
            category: str = "",
            upload_date: Optional[datetime] = None,
            **kwargs
    ):
        self.title = title
        self.url = url
        self.size = size
        self.seeders = seeders
        self.leechers = leechers
        self.source = source
        self.quality = quality
        self.category = category
        self.upload_date = upload_date
        self.extra_data = kwargs

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "title": self.title,
            "url": self.url,
            "size": self.size,
            "seeders": self.seeders,
            "leechers": self.leechers,
            "source": self.source,
            "quality": self.quality,
            "category": self.category,
            "upload_date": self.upload_date.isoformat() if self.upload_date else None,
            **self.extra_data
        }


class SearchEngine(ABC):
    """搜索引擎抽象基类"""

    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled

    @abstractmethod
    async def search(
            self,
            query: str,
            category: Optional[str] = None,
            limit: int = 50
    ) -> List[SearchResult]:
        """搜索方法"""
        pass

    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接"""
        pass


class TMDBSearchEngine(SearchEngine):
    """TMDB搜索引擎"""

    def __init__(self):
        super().__init__("TMDB", enabled=tmdb_service.is_configured)

    async def search(
            self,
            query: str,
            category: Optional[str] = None,
            limit: int = 50
    ) -> List[SearchResult]:
        """搜索TMDB内容"""
        if not self.enabled:
            return []

        try:
            results = []

            if category == "movie":
                data = await tmdb_service.search_movie(query, page=1)
            elif category == "tv":
                data = await tmdb_service.search_tv(query, page=1)
            else:
                data = await tmdb_service.search_multi(query, page=1)

            for item in data.get("results", [])[:limit]:
                media_type = item.get("media_type", category or "unknown")

                if media_type == "movie":
                    title = item.get("title", "")
                    release_date = item.get("release_date", "")
                elif media_type == "tv":
                    title = item.get("name", "")
                    release_date = item.get("first_air_date", "")
                else:
                    continue

                result = SearchResult(
                    title=title,
                    url=f"https://www.themoviedb.org/{media_type}/{item.get('id')}",
                    source="TMDB",
                    category=media_type,
                    upload_date=datetime.fromisoformat(release_date) if release_date else None,
                    tmdb_id=item.get("id"),
                    overview=item.get("overview", ""),
                    poster_path=tmdb_service.get_image_url(item.get("poster_path", "")),
                    vote_average=item.get("vote_average", 0),
                    popularity=item.get("popularity", 0)
                )

                results.append(result)

            return results

        except Exception as e:
            logger.error(f"TMDB搜索失败: {e}")
            return []

    async def test_connection(self) -> bool:
        """测试TMDB连接"""
        try:
            await tmdb_service.get_popular_movies(page=1)
            return True
        except Exception:
            return False


class TelegramSearchEngine(SearchEngine):
    """Telegram搜索引擎"""

    def __init__(self):
        super().__init__("Telegram", enabled=telegram_service.enabled)

    async def search(
            self,
            query: str,
            category: Optional[str] = None,
            limit: int = 50
    ) -> List[SearchResult]:
        """搜索Telegram频道内容"""
        if not self.enabled:
            return []

        try:
            # 这里需要实现具体的Telegram频道搜索逻辑
            # 由于Bot API限制，可能需要使用Telegram Client API
            logger.warning("Telegram搜索功能需要进一步实现")
            return []

        except Exception as e:
            logger.error(f"Telegram搜索失败: {e}")
            return []

    async def test_connection(self) -> bool:
        """测试Telegram连接"""
        return await telegram_service.test_connection()


class MockBTSearchEngine(SearchEngine):
    """模拟BT搜索引擎（用于演示）"""

    def __init__(self):
        super().__init__("MockBT", enabled=True)

    async def search(
            self,
            query: str,
            category: Optional[str] = None,
            limit: int = 50
    ) -> List[SearchResult]:
        """模拟BT搜索"""
        # 模拟搜索结果
        mock_results = [
            SearchResult(
                title=f"{query} (2023) 1080p BluRay x264",
                url="magnet:?xt=urn:btih:example1",
                size="2.1 GB",
                seeders=156,
                leechers=23,
                source="MockBT",
                quality="1080p",
                category="movie"
            ),
            SearchResult(
                title=f"{query} S01 Complete 720p WEB-DL",
                url="magnet:?xt=urn:btih:example2",
                size="8.5 GB",
                seeders=89,
                leechers=12,
                source="MockBT",
                quality="720p",
                category="tv"
            ),
            SearchResult(
                title=f"{query} [1-12话] 1080p BDRip",
                url="magnet:?xt=urn:btih:example3",
                size="12.3 GB",
                seeders=234,
                leechers=45,
                source="MockBT",
                quality="1080p",
                category="anime"
            )
        ]

        # 根据类别过滤
        if category:
            mock_results = [r for r in mock_results if r.category == category]

        return mock_results[:limit]

    async def test_connection(self) -> bool:
        """测试连接（模拟总是成功）"""
        return True


class SearchService(Singleton):
    """搜索服务主类"""

    def __init__(self):
        self.engines: Dict[str, SearchEngine] = {}
        self._register_engines()

    def _register_engines(self):
        """注册搜索引擎"""
        engines = [
            TMDBSearchEngine(),
            TelegramSearchEngine(),
            MockBTSearchEngine(),
        ]

        for engine in engines:
            self.engines[engine.name] = engine
            logger.info(f"注册搜索引擎: {engine.name} (启用: {engine.enabled})")

    async def search(
            self,
            query: str,
            sources: Optional[List[str]] = None,
            category: Optional[str] = None,
            limit: int = 50
    ) -> List[Dict[str, Any]]:
        """执行搜索"""
        if not query.strip():
            return []

        # 确定要使用的搜索引擎
        if sources:
            engines_to_use = [
                self.engines[name] for name in sources
                if name in self.engines and self.engines[name].enabled
            ]
        else:
            engines_to_use = [
                engine for engine in self.engines.values()
                if engine.enabled
            ]

        if not engines_to_use:
            logger.warning("没有可用的搜索引擎")
            return []

        # 并发搜索
        tasks = [
            engine.search(query, category, limit)
            for engine in engines_to_use
        ]

        try:
            results_lists = await asyncio.gather(*tasks, return_exceptions=True)

            # 合并结果
            all_results = []
            for i, results in enumerate(results_lists):
                if isinstance(results, Exception):
                    logger.error(f"搜索引擎 {engines_to_use[i].name} 出错: {results}")
                    continue

                all_results.extend([result.to_dict() for result in results])

            # 按相关性和做种数排序
            all_results.sort(
                key=lambda x: (x.get("seeders", 0), x.get("popularity", 0)),
                reverse=True
            )

            return all_results[:limit]

        except Exception as e:
            logger.error(f"搜索过程出错: {e}")
            return []

    async def get_available_sources(self) -> List[Dict[str, Any]]:
        """获取可用的搜索源"""
        sources = []

        for name, engine in self.engines.items():
            # 测试连接状态
            try:
                is_connected = await engine.test_connection()
            except Exception:
                is_connected = False

            sources.append({
                "name": name,
                "display_name": name,
                "enabled": engine.enabled,
                "connected": is_connected
            })

        return sources

    def get_engine(self, name: str) -> Optional[SearchEngine]:
        """获取指定的搜索引擎"""
        return self.engines.get(name)


# 向后兼容
search_service = SearchService()
