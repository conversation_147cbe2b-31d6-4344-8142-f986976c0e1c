"""
Alembic环境配置
用于数据库迁移管理
"""

import asyncio
import os
import sys
from logging.config import fileConfig
from pathlib import Path

from alembic import context
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入模型和配置
from backend.database.database import Base
from backend.utils.config import settings

# 导入所有模型以确保它们被注册

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata


# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_database_url():
    """获取数据库URL"""
    # 优先使用环境变量
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        return database_url

    # 使用配置文件中的URL
    return settings.DATABASE_URL


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        render_as_batch=True,  # 支持SQLite的ALTER TABLE
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行迁移"""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
        render_as_batch=True,  # 支持SQLite的ALTER TABLE
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """Run migrations in 'online' mode with async engine."""

    # 获取数据库URL
    database_url = get_database_url()

    # 创建异步引擎配置
    configuration = config.get_section(config.config_ini_section, {})
    configuration["sqlalchemy.url"] = database_url

    # 如果是SQLite，使用同步模式
    if database_url.startswith("sqlite"):
        from sqlalchemy import create_engine

        connectable = create_engine(
            database_url,
            poolclass=pool.NullPool,
            connect_args={"check_same_thread": False}
        )

        with connectable.connect() as connection:
            do_run_migrations(connection)
    else:
        # PostgreSQL等其他数据库使用异步模式
        connectable = async_engine_from_config(
            configuration,
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )

        async with connectable.connect() as connection:
            await connection.run_sync(do_run_migrations)

        await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""

    # 获取数据库URL
    database_url = get_database_url()

    # 如果是SQLite，使用同步模式
    if database_url.startswith("sqlite"):
        from sqlalchemy import create_engine

        connectable = create_engine(
            database_url,
            poolclass=pool.NullPool,
            connect_args={"check_same_thread": False}
        )

        with connectable.connect() as connection:
            do_run_migrations(connection)
    else:
        # 其他数据库使用异步模式
        asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
