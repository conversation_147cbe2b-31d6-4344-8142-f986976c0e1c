#!/usr/bin/env python3
"""
MediPaka 依赖安装脚本
使用pyproject.toml统一管理依赖，支持不同的安装模式
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔄 {description or command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ 成功: {description or command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {description or command}")
        print(f"错误输出: {e.stderr}")
        return False


def install_requirements(mode="basic"):
    """安装依赖"""
    backend_dir = Path(__file__).parent / "backend"

    if not backend_dir.exists():
        print("❌ 找不到backend目录")
        return False

    # 构建安装命令
    if mode == "basic":
        # 仅安装基础依赖
        command = f"pip install -e {backend_dir}"
        description = "安装基础依赖"
    elif mode == "dev":
        # 安装开发依赖
        command = f"pip install -e '{backend_dir}[dev]'"
        description = "安装开发依赖"
    elif mode == "full":
        # 安装完整功能
        command = f"pip install -e '{backend_dir}[full]'"
        description = "安装完整功能依赖"
    elif mode == "all":
        # 安装所有依赖（开发+完整功能）
        command = f"pip install -e '{backend_dir}[all]'"
        description = "安装所有依赖"
    else:
        print(f"❌ 未知的安装模式: {mode}")
        return False

    print(f"\n📦 {description}...")
    return run_command(command, description)


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ 需要Python 3.9或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False

    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def upgrade_pip():
    """升级pip"""
    print("🔄 升级pip...")
    return run_command("pip install --upgrade pip", "升级pip")


def show_summary(mode):
    """显示安装总结"""
    print(f"\n{'='*50}")
    print("📋 安装总结")
    print('='*50)

    if mode == "basic":
        print("✅ 基础运行时依赖已安装")
        print("💡 包含: FastAPI, SQLAlchemy, 认证等核心功能")
    elif mode == "dev":
        print("✅ 基础 + 开发依赖已安装")
        print("💡 包含: 测试框架, 代码质量工具, 调试工具等")
    elif mode == "full":
        print("✅ 完整功能依赖已安装")
        print("💡 包含: 媒体处理, 下载客户端, 通知服务等")
    elif mode == "all":
        print("✅ 所有依赖已安装")
        print("💡 包含: 开发工具 + 完整功能模块")

    print(f"\n🚀 启动命令:")
    print(f"  开发环境: python backend/start.py --env dev")
    print(f"  生产环境: python backend/start.py --env prod")
    print(f"  直接启动: python backend/main.py")

    if mode in ["dev", "all"]:
        print(f"\n🧪 测试命令:")
        print(f"  运行测试: pytest backend/tests/")
        print(f"  代码检查: black backend/ && flake8 backend/")

    print(f"\n📦 可选功能安装:")
    print(f"  媒体功能: pip install -e 'backend[media]'")
    print(f"  下载客户端: pip install -e 'backend[downloaders]'")
    print(f"  通知服务: pip install -e 'backend[notifications]'")
    print(f"  任务调度: pip install -e 'backend[scheduler]'")

    print(f"\n💡 pyproject.toml管理优势:")
    print(f"  - 统一的依赖管理")
    print(f"  - 模块化的可选依赖")
    print(f"  - 标准化的Python项目结构")
    print(f"  - 更好的版本控制")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MediPaka 依赖安装脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
安装模式说明:
  basic  - 仅安装运行时必需依赖 (默认)
  dev    - 安装运行时 + 开发依赖
  full   - 安装运行时 + 完整功能依赖
  all    - 安装所有依赖 (开发 + 完整功能)

示例:
  python install.py                # 基础安装
  python install.py --mode dev     # 开发环境
  python install.py --mode full    # 完整功能
  python install.py --mode all     # 所有依赖

使用pyproject.toml的优势:
  - 统一的依赖管理，无需多个requirements文件
  - 模块化的可选依赖，按需安装
  - 符合Python标准，更好的工具支持
        """
    )

    parser.add_argument(
        "--mode",
        choices=["basic", "dev", "full", "all"],
        default="basic",
        help="安装模式 (默认: basic)"
    )

    parser.add_argument(
        "--skip-upgrade",
        action="store_true",
        help="跳过pip升级"
    )

    parser.add_argument(
        "--skip-check",
        action="store_true",
        help="跳过Python版本检查"
    )

    args = parser.parse_args()

    print("🚀 MediPaka 依赖安装脚本")
    print(f"📦 安装模式: {args.mode}")

    # 检查Python版本
    if not args.skip_check and not check_python_version():
        sys.exit(1)

    # 升级pip
    if not args.skip_upgrade and not upgrade_pip():
        print("⚠️ pip升级失败，继续安装...")

    # 安装依赖
    if install_requirements(args.mode):
        show_summary(args.mode)
        print("\n🎉 依赖安装完成！")
        sys.exit(0)
    else:
        print("\n❌ 依赖安装失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
