"""
Subscription API endpoints
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from backend.database import Subscription
from backend.database.session import get_db
from backend.schemas import Response
from backend.services.tmdb import tmdb_service

router = APIRouter()


class SubscriptionCreate(BaseModel):
    name: str
    type: str = "manual"
    tmdb_id: Optional[int] = None
    douban_id: Optional[str] = None
    mikan_id: Optional[str] = None
    description: Optional[str] = None
    keywords: Optional[List[str]] = None
    exclude_keywords: Optional[List[str]] = None
    quality_filter: Optional[str] = None
    auto_download: bool = True
    auto_rename: bool = True
    auto_upload: bool = True


class SubscriptionUpdate(BaseModel):
    name: Optional[str] = None
    status: Optional[str] = None
    keywords: Optional[List[str]] = None
    exclude_keywords: Optional[List[str]] = None
    quality_filter: Optional[str] = None
    auto_download: Optional[bool] = None
    auto_rename: Optional[bool] = None
    auto_upload: Optional[bool] = None


class SubscriptionResponse(BaseModel):
    id: int
    name: str
    type: str
    status: str
    tmdb_id: Optional[int]
    description: Optional[str]
    poster_url: Optional[str]
    year: Optional[int]
    auto_download: bool
    auto_rename: bool
    auto_upload: bool

    class Config:
        from_attributes = True


@router.get("/list", response_model=Response)
async def get_subscriptions(
        skip: int = 0,
        limit: int = 100,
        state: Optional[str] = None,
        db: Session = Depends(get_db)
):
    """
    Get all subscriptions
    """
    try:
        query = db.query(Subscription)

        if state:
            query = query.filter(Subscription.status == state)

        subscriptions = query.offset(skip).limit(limit).all()

        return Response(
            message="获取订阅列表成功",
            data=[sub.to_dict() if hasattr(sub, 'to_dict') else {
                "id": sub.id,
                "name": sub.name,
                "type": sub.type,
                "status": sub.status,
                "tmdb_id": sub.tmdb_id,
                "description": sub.description,
                "poster_url": sub.poster_url,
                "year": sub.year,
                "auto_download": sub.auto_download,
                "auto_rename": sub.auto_rename,
                "auto_upload": sub.auto_upload,
            } for sub in subscriptions]
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订阅列表失败"
        )


@router.post("/create", response_model=Response)
async def create_subscription(
        subscription: SubscriptionCreate,
        db: Session = Depends(get_db)
):
    """
    Create a new subscription
    """
    try:
        poster_url = None

        # If TMDB ID is provided, fetch metadata
        if subscription.tmdb_id:
            if subscription.type in ["movie", "tv"]:
                if subscription.type == "movie":
                    tmdb_data = await tmdb_service.get_movie_details(subscription.tmdb_id)
                else:
                    tmdb_data = await tmdb_service.get_tv_details(subscription.tmdb_id)

                if tmdb_data:
                    # Update subscription with TMDB data
                    if not subscription.description:
                        subscription.description = tmdb_data.get("overview")

                    # Set poster URL
                    poster_path = tmdb_data.get("poster_path")
                    poster_url = tmdb_service.get_image_url(poster_path) if poster_path else None

        # Create subscription
        db_subscription = Subscription(
            name=subscription.name,
            type=subscription.type,
            tmdb_id=subscription.tmdb_id,
            douban_id=subscription.douban_id,
            mikan_id=subscription.mikan_id,
            description=subscription.description,
            poster_url=poster_url,
            keywords=subscription.keywords,
            exclude_keywords=subscription.exclude_keywords,
            quality_filter=subscription.quality_filter,
            auto_download=subscription.auto_download,
            auto_rename=subscription.auto_rename,
            auto_upload=subscription.auto_upload,
        )

        db.add(db_subscription)
        db.commit()
        db.refresh(db_subscription)

        return Response(
            message="创建订阅成功",
            data=db_subscription.to_dict() if hasattr(db_subscription, 'to_dict') else {
                "id": db_subscription.id,
                "name": db_subscription.name,
                "type": db_subscription.type,
                "status": db_subscription.status,
                "tmdb_id": db_subscription.tmdb_id,
                "description": db_subscription.description,
                "poster_url": db_subscription.poster_url,
                "year": db_subscription.year,
                "auto_download": db_subscription.auto_download,
                "auto_rename": db_subscription.auto_rename,
                "auto_upload": db_subscription.auto_upload,
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建订阅失败"
        )


@router.get("/get/{subscription_id}", response_model=Response)
async def get_subscription(
        subscription_id: int,
        db: Session = Depends(get_db)
):
    """
    Get a specific subscription
    """
    try:
        subscription = db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )

        return Response(
            message="获取订阅成功",
            data=subscription.to_dict() if hasattr(subscription, 'to_dict') else {
                "id": subscription.id,
                "name": subscription.name,
                "type": subscription.type,
                "status": subscription.status,
                "tmdb_id": subscription.tmdb_id,
                "description": subscription.description,
                "poster_url": subscription.poster_url,
                "year": subscription.year,
                "auto_download": subscription.auto_download,
                "auto_rename": subscription.auto_rename,
                "auto_upload": subscription.auto_upload,
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订阅失败"
        )


@router.put("/update/{subscription_id}", response_model=Response)
async def update_subscription(
        subscription_id: int,
        subscription_update: SubscriptionUpdate,
        db: Session = Depends(get_db)
):
    """
    Update a subscription
    """
    try:
        subscription = db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )

        # Update fields
        update_data = subscription_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(subscription, field, value)

        db.commit()
        db.refresh(subscription)

        return Response(
            message="更新订阅成功",
            data=subscription.to_dict() if hasattr(subscription, 'to_dict') else {
                "id": subscription.id,
                "name": subscription.name,
                "type": subscription.type,
                "status": subscription.status,
                "tmdb_id": subscription.tmdb_id,
                "description": subscription.description,
                "poster_url": subscription.poster_url,
                "year": subscription.year,
                "auto_download": subscription.auto_download,
                "auto_rename": subscription.auto_rename,
                "auto_upload": subscription.auto_upload,
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新订阅失败"
        )


@router.delete("/delete/{subscription_id}", response_model=Response)
async def delete_subscription(
        subscription_id: int,
        db: Session = Depends(get_db)
):
    """
    Delete a subscription
    """
    try:
        subscription = db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )

        db.delete(subscription)
        db.commit()

        return Response(
            message="删除订阅成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除订阅失败"
        )


@router.post("/tmdb", response_model=Response)
async def search_tmdb(
        query: str,
        media_type: str = "multi",  # movie, tv, or multi
        year: Optional[int] = None
):
    """
    Search TMDB for media to subscribe to
    """
    results = []

    if media_type in ["movie", "multi"]:
        movie_data = await tmdb_service.search_movie(query, year=year)
        for movie in movie_data.get("results", []):
            results.append({
                "id": movie["id"],
                "title": movie["title"],
                "type": "movie",
                "year": movie.get("release_date", "")[:4] if movie.get("release_date") else None,
                "overview": movie.get("overview"),
                "poster_url": movie.get("poster_path"),
            })

    if media_type in ["tv", "multi"]:
        tv_data = await tmdb_service.search_tv(query, first_air_date_year=year)
        for show in tv_data.get("results", []):
            results.append({
                "id": show["id"],
                "title": show["name"],
                "type": "tv",
                "year": show.get("first_air_date", "")[:4] if show.get("first_air_date") else None,
                "overview": show.get("overview"),
                "poster_url": show.get("poster_path"),
            })

    return Response(
        message="成功",
        data=results
    )
