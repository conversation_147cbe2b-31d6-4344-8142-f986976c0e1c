"""
认证API路由
"""

from typing import Annotated

from fastapi import APIRouter, Depends, Request, status, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from backend.database import get_db
from backend.database.models import User
from backend.schemas import Response
from backend.services import auth_service
from backend.services.auth import (
    AuthError,
    AuthenticationError,
    UserInactiveError,
    PasswordError
)

router = APIRouter()


def get_current_user(request: Request) -> User:
    """从中间件获取当前用户"""
    from backend.middleware import get_current_user_from_request

    user = get_current_user_from_request(request)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未找到用户信息",
        )
    return user


# 类型注解
CurrentUser = Annotated[User, Depends(get_current_user)]


# 请求模型
class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, description="密码")


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., description="原密码")
    new_password: str = Field(..., min_length=6, description="新密码")


@router.post("/login", response_model=Response)
async def login(
        login_data: LoginRequest,
        db: Session = Depends(get_db)
):
    """管理员登录"""
    try:
        # 验证用户
        user = auth_service.authenticate_user(db, login_data.username, login_data.password)

        # 使之前的所有token失效
        auth_service.invalidate_previous_tokens(db, user)

        # 创建访问令牌
        access_token = auth_service.create_access_token(user)

        # 更新用户登录信息
        User.update_login_info(db, user)

        return Response(
            message="登录成功",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 24 * 60 * 60,  # 24小时
                "user": user.to_dict()
            }
        )

    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except UserInactiveError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except AuthError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.get("/me", response_model=Response)
async def get_current_user_info(current_user: CurrentUser):
    """获取当前用户信息"""
    return Response(
        message="获取用户信息成功",
        data=current_user.to_dict()
    )


@router.post("/change", response_model=Response)
async def change_password(
        request_data: ChangePasswordRequest,
        current_user: CurrentUser,
        db: Session = Depends(get_db)
):
    """修改密码"""
    try:
        auth_service.change_password(
            db, current_user,
            request_data.old_password,
            request_data.new_password
        )
        return Response(message="密码修改成功")
    except PasswordError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except AuthError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试"
        )


@router.get("/verify", response_model=Response)
async def verify_token(current_user: CurrentUser):
    """验证令牌有效性"""
    return Response(
        message="令牌有效",
        data={
            "valid": True,
            "user": current_user.to_dict()
        }
    )


@router.post("/logout", response_model=Response)
async def logout(
        current_user: CurrentUser,
        db: Session = Depends(get_db)
):
    """用户登出"""
    try:
        auth_service.logout_user(db, current_user)
        return Response(message="登出成功")
    except AuthError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败，请稍后重试"
        )
