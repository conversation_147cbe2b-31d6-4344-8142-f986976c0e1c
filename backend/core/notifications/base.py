"""
Base notification class
"""
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any


class NotificationPriority(str, Enum):
    """Notification priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class BaseNotification(ABC):
    """
    Abstract base class for all notification providers
    """

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.is_configured = False

    @abstractmethod
    async def send_message(
            self,
            title: str,
            message: str,
            priority: NotificationPriority = NotificationPriority.NORMAL,
            **kwargs
    ) -> bool:
        """
        Send a notification message
        
        Args:
            title: Message title
            message: Message content
            priority: Message priority
            **kwargs: Additional parameters specific to the provider
            
        Returns:
            True if message sent successfully, False otherwise
        """
        pass

    @abstractmethod
    async def test_connection(self) -> bool:
        """
        Test connection to the notification service
        
        Returns:
            True if connection is working, False otherwise
        """
        pass

    async def send_download_started(self, download_name: str, **kwargs) -> bool:
        """
        Send download started notification
        """
        return await self.send_message(
            title="下载开始",
            message=f"开始下载: {download_name}",
            priority=NotificationPriority.NORMAL,
            **kwargs
        )

    async def send_download_completed(self, download_name: str, **kwargs) -> bool:
        """
        Send download completed notification
        """
        return await self.send_message(
            title="下载完成",
            message=f"下载完成: {download_name}",
            priority=NotificationPriority.NORMAL,
            **kwargs
        )

    async def send_download_failed(self, download_name: str, error: str, **kwargs) -> bool:
        """
        Send download failed notification
        """
        return await self.send_message(
            title="下载失败",
            message=f"下载失败: {download_name}\n错误: {error}",
            priority=NotificationPriority.HIGH,
            **kwargs
        )

    async def send_subscription_update(self, subscription_name: str, new_episodes: int, **kwargs) -> bool:
        """
        Send subscription update notification
        """
        return await self.send_message(
            title="订阅更新",
            message=f"{subscription_name} 有 {new_episodes} 个新剧集",
            priority=NotificationPriority.NORMAL,
            **kwargs
        )

    async def send_system_error(self, error_message: str, **kwargs) -> bool:
        """
        Send system error notification
        """
        return await self.send_message(
            title="系统错误",
            message=f"系统发生错误: {error_message}",
            priority=NotificationPriority.URGENT,
            **kwargs
        )
