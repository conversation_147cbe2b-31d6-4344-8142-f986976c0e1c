"""
Mikan RSS parser for anime subscriptions
"""
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

import aiohttp
import feedparser
from loguru import logger  as logger

from backend.database import settings


class MikanRSSParser:
    """
    Mikan RSS parser for fetching anime updates
    """

    def __init__(self):
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def parse_rss_feed(self, rss_url: str) -> List[Dict[str, Any]]:
        """
        Parse Mikan RSS feed and extract episode information
        
        Args:
            rss_url: Mikan RSS feed URL
            
        Returns:
            List of episode information
        """
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(rss_url) as response:
                if response.status != 200:
                    logger.error(f"Failed to fetch RSS feed: {response.status}")
                    return []

                content = await response.text()

            # Parse RSS feed
            feed = feedparser.parse(content)

            episodes = []
            for entry in feed.entries:
                episode_info = self._extract_episode_info(entry)
                if episode_info:
                    episodes.append(episode_info)

            logger.info(f"Parsed {len(episodes)} episodes from Mikan RSS")
            return episodes

        except Exception as e:
            logger.error(f"Error parsing RSS feed {rss_url}: {e}")
            return []

    def _extract_episode_info(self, entry) -> Optional[Dict[str, Any]]:
        """
        Extract episode information from RSS entry
        
        Args:
            entry: RSS feed entry
            
        Returns:
            Episode information dictionary
        """
        try:
            title = entry.title
            link = entry.link
            description = getattr(entry, 'description', '')
            pub_date = getattr(entry, 'published_parsed', None)

            # Extract magnet link from description or enclosure
            magnet_link = self._extract_magnet_link(entry)

            # Parse episode information from title
            episode_data = self._parse_episode_title(title)

            # Extract file size if available
            file_size = self._extract_file_size(description)

            return {
                "title": title,
                "link": link,
                "magnet_link": magnet_link,
                "description": description,
                "pub_date": datetime(*pub_date[:6]) if pub_date else None,
                "file_size": file_size,
                **episode_data
            }

        except Exception as e:
            logger.error(f"Error extracting episode info: {e}")
            return None

    def _extract_magnet_link(self, entry) -> Optional[str]:
        """
        Extract magnet link from RSS entry
        """
        # Check enclosures first
        if hasattr(entry, 'enclosures') and entry.enclosures:
            for enclosure in entry.enclosures:
                if enclosure.href.startswith('magnet:'):
                    return enclosure.href

        # Check description for magnet links
        description = getattr(entry, 'description', '')
        magnet_match = re.search(r'magnet:\?[^"<>\s]+', description)
        if magnet_match:
            return magnet_match.group(0)

        # Check link if it's a magnet link
        link = getattr(entry, 'link', '')
        if link.startswith('magnet:'):
            return link

        return None

    def _parse_episode_title(self, title: str) -> Dict[str, Any]:
        """
        Parse episode information from title
        
        Args:
            title: Episode title
            
        Returns:
            Parsed episode information
        """
        result = {
            "anime_name": "",
            "episode_number": None,
            "season_number": None,
            "quality": None,
            "source": None,
            "subtitle_group": None,
        }

        # Extract subtitle group (usually in brackets at the beginning)
        subtitle_group_match = re.match(r'^\[([^\]]+)\]', title)
        if subtitle_group_match:
            result["subtitle_group"] = subtitle_group_match.group(1)

        # Extract episode number
        episode_patterns = [
            r'第(\d+)话',  # Chinese: 第X话
            r'第(\d+)集',  # Chinese: 第X集
            r'EP?\.?(\d+)',  # EP.X or E.X
            r'Episode\s*(\d+)',  # Episode X
            r'\s(\d+)\s',  # Simple number
        ]

        for pattern in episode_patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                result["episode_number"] = int(match.group(1))
                break

        # Extract quality
        quality_patterns = [
            r'1080[pP]',
            r'720[pP]',
            r'480[pP]',
            r'4K',
            r'2160[pP]',
        ]

        for pattern in quality_patterns:
            if re.search(pattern, title):
                result["quality"] = re.search(pattern, title).group(0)
                break

        # Extract source
        source_patterns = [
            r'BluRay',
            r'BDRip',
            r'WEB-DL',
            r'WEBRip',
            r'HDTV',
            r'DVDRip',
        ]

        for pattern in source_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                result["source"] = re.search(pattern, title, re.IGNORECASE).group(0)
                break

        # Extract anime name (remove subtitle group and episode info)
        anime_name = title
        if subtitle_group_match:
            anime_name = anime_name[subtitle_group_match.end():].strip()

        # Remove episode information
        for pattern in episode_patterns:
            anime_name = re.sub(pattern, '', anime_name, flags=re.IGNORECASE)

        # Clean up anime name
        anime_name = re.sub(r'\s+', ' ', anime_name).strip()
        result["anime_name"] = anime_name

        return result

    def _extract_file_size(self, description: str) -> Optional[str]:
        """
        Extract file size from description
        """
        size_patterns = [
            r'(\d+(?:\.\d+)?)\s*GB',
            r'(\d+(?:\.\d+)?)\s*MB',
            r'(\d+(?:\.\d+)?)\s*KB',
        ]

        for pattern in size_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return match.group(0)

        return None

    async def get_all_subscribed_feeds(self) -> List[Dict[str, Any]]:
        """
        Get all episodes from subscribed Mikan RSS feeds
        """
        all_episodes = []

        for rss_url in settings.mikan_rss_urls:
            episodes = await self.parse_rss_feed(rss_url)
            all_episodes.extend(episodes)

        # Sort by publication date (newest first)
        all_episodes.sort(
            key=lambda x: x.get('pub_date') or datetime.min,
            reverse=True
        )

        return all_episodes


# Global Mikan parser instance
mikan_parser = MikanRSSParser()
