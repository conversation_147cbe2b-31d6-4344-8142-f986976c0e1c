"""
Core模块
包含核心业务逻辑和底层功能模块
"""

# 导入核心组件
from . import downloaders
from . import notifications
from . import subscription

# 为了向后兼容，从services导入主要服务
from ..services import (
    tmdb_service, TMDBService, TMDBError,
    telegram_service, TelegramService, TelegramError,
    search_service, SearchService, SearchResult, SearchEngine
)

__all__ = [
    # 核心模块
    "downloaders",
    "notifications",
    "subscription",

    # 服务（向后兼容）
    "tmdb_service",
    "TMDBService",
    "TMDBError",
    "telegram_service",
    "TelegramService",
    "TelegramError",
    "search_service",
    "SearchService",
    "SearchResult",
    "SearchEngine",
]
