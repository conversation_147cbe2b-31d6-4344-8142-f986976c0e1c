"""
qBittorrent downloader implementation
"""
import asyncio
from typing import Dict, Any, List, Optional

from loguru import logger  as logger
from qbittorrentapi import Client

from .base import BaseDownloader, DownloadStatus
from backend.database import settings


class QBittorrentDownloader(BaseDownloader):
    """
    qBittorrent downloader implementation
    """

    def __init__(self):
        config = {
            "host": settings.qbittorrent.host,
            "port": settings.qbittorrent.port,
            "username": settings.qbittorrent.username,
            "password": settings.qbittorrent.password,
        }
        super().__init__("qbittorrent", config)
        self.client = None

    async def connect(self) -> bool:
        """
        Connect to qBittorrent client
        """
        try:
            self.client = Client(
                host=self.config["host"],
                port=self.config["port"],
                username=self.config["username"],
                password=self.config["password"],
            )

            # Test connection
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.auth_log_in
            )

            self.is_connected = True
            logger.info("Connected to qBittorrent successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to qBittorrent: {e}")
            self.is_connected = False
            return False

    async def disconnect(self) -> None:
        """
        Disconnect from qBittorrent client
        """
        if self.client:
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.client.auth_log_out
                )
            except Exception as e:
                logger.error(f"Error disconnecting from qBittorrent: {e}")
            finally:
                self.client = None
                self.is_connected = False

    async def add_download(self, url: str, save_path: Optional[str] = None, **kwargs) -> Optional[str]:
        """
        Add a new download to qBittorrent
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return None

        try:
            # Prepare download parameters
            download_params = {
                "urls": url,
                "save_path": save_path or settings.qbittorrent.download_path,
            }

            # Add additional parameters
            if "category" in kwargs:
                download_params["category"] = kwargs["category"]
            if "tags" in kwargs:
                download_params["tags"] = kwargs["tags"]

            # Add torrent
            result = await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.client.torrents_add(**download_params)
            )

            if result == "Ok.":
                # Get the torrent hash (this is a bit tricky with qBittorrent API)
                # We'll need to find the torrent by URL or name
                torrents = await asyncio.get_event_loop().run_in_executor(
                    None, self.client.torrents_info
                )

                # Find the most recently added torrent
                if torrents:
                    latest_torrent = max(torrents, key=lambda t: t.added_on)
                    logger.info(f"Added download: {latest_torrent.name}")
                    return latest_torrent.hash

            return None

        except Exception as e:
            logger.error(f"Failed to add download: {e}")
            return None

    async def get_download_status(self, download_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific download
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return None

        try:
            torrent = await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.client.torrents_info(torrent_hashes=download_id)
            )

            if not torrent:
                return None

            torrent = torrent[0]

            return {
                "id": torrent.hash,
                "name": torrent.name,
                "status": self.normalize_status(torrent.state),
                "progress": torrent.progress * 100,  # Convert to percentage
                "size": torrent.size,
                "downloaded": torrent.downloaded,
                "download_speed": torrent.dlspeed,
                "upload_speed": torrent.upspeed,
                "eta": torrent.eta if torrent.eta != 8640000 else None,  # qBittorrent uses 8640000 for infinity
                "ratio": torrent.ratio,
                "seeds": torrent.num_seeds,
                "peers": torrent.num_leechs,
                "save_path": torrent.save_path,
            }

        except Exception as e:
            logger.error(f"Failed to get download status: {e}")
            return None

    async def get_all_downloads(self) -> List[Dict[str, Any]]:
        """
        Get status of all downloads
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return []

        try:
            torrents = await asyncio.get_event_loop().run_in_executor(
                None, self.client.torrents_info
            )

            downloads = []
            for torrent in torrents:
                downloads.append({
                    "id": torrent.hash,
                    "name": torrent.name,
                    "status": self.normalize_status(torrent.state),
                    "progress": torrent.progress * 100,
                    "size": torrent.size,
                    "downloaded": torrent.downloaded,
                    "download_speed": torrent.dlspeed,
                    "upload_speed": torrent.upspeed,
                    "eta": torrent.eta if torrent.eta != 8640000 else None,
                    "ratio": torrent.ratio,
                    "seeds": torrent.num_seeds,
                    "peers": torrent.num_leechs,
                    "save_path": torrent.save_path,
                })

            return downloads

        except Exception as e:
            logger.error(f"Failed to get all downloads: {e}")
            return []

    async def pause_download(self, download_id: str) -> bool:
        """
        Pause a download
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.client.torrents_pause(torrent_hashes=download_id)
            )
            return True
        except Exception as e:
            logger.error(f"Failed to pause download: {e}")
            return False

    async def resume_download(self, download_id: str) -> bool:
        """
        Resume a paused download
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.client.torrents_resume(torrent_hashes=download_id)
            )
            return True
        except Exception as e:
            logger.error(f"Failed to resume download: {e}")
            return False

    async def remove_download(self, download_id: str, delete_files: bool = False) -> bool:
        """
        Remove a download
        """
        if not self.is_connected or not self.client:
            if not await self.connect():
                return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.client.torrents_delete(
                    torrent_hashes=download_id,
                    delete_files=delete_files
                )
            )
            return True
        except Exception as e:
            logger.error(f"Failed to remove download: {e}")
            return False

    def normalize_status(self, qb_status: str) -> DownloadStatus:
        """
        Normalize qBittorrent status to standard status
        """
        status_map = {
            "downloading": DownloadStatus.DOWNLOADING,
            "uploading": DownloadStatus.SEEDING,
            "pausedDL": DownloadStatus.PAUSED,
            "pausedUP": DownloadStatus.PAUSED,
            "queuedDL": DownloadStatus.PENDING,
            "queuedUP": DownloadStatus.PENDING,
            "stalledDL": DownloadStatus.DOWNLOADING,
            "stalledUP": DownloadStatus.SEEDING,
            "checkingDL": DownloadStatus.DOWNLOADING,
            "checkingUP": DownloadStatus.SEEDING,
            "error": DownloadStatus.FAILED,
            "missingFiles": DownloadStatus.FAILED,
        }

        return status_map.get(qb_status, DownloadStatus.PENDING)


# Global qBittorrent downloader instance
qb_downloader = QBittorrentDownloader()
