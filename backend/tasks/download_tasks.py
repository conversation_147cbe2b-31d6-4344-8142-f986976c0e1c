"""
Download-related background tasks
"""
from loguru import logger  as logger
from sqlalchemy.sql import func

from backend.database.database import <PERSON><PERSON>ocal
from ..core.downloaders.qbittorrent import qb_downloader
from ..core.notifications.telegram import telegram_notification
from backend.schemas import Download


async def update_download_status():
    """
    Update status of all active downloads
    """
    try:
        logger.debug("Updating download status...")

        db = SessionLocal()
        try:
            # Get all active downloads
            active_downloads = db.query(Download).filter(
                Download.status.in_(["pending", "downloading", "paused"])
            ).all()

            updated_count = 0

            for download in active_downloads:
                if download.client_type == "qbittorrent" and download.client_id:
                    try:
                        # Get status from qBittorrent
                        status = await qb_downloader.get_download_status(download.client_id)

                        if status:
                            # Update download record
                            old_status = download.status
                            download.status = status["status"]
                            download.progress = status["progress"]
                            download.size = status["size"]
                            download.downloaded = status["downloaded"]
                            download.download_speed = status["download_speed"]
                            download.upload_speed = status["upload_speed"]
                            download.eta = status["eta"]
                            download.ratio = status["ratio"]
                            download.seeds = status["seeds"]
                            download.peers = status["peers"]
                            download.updated_at = func.now()

                            # Check if status changed to completed
                            if old_status != "completed" and download.status == "completed":
                                download.completed_at = func.now()

                                # Send completion notification
                                await telegram_notification.send_download_completed(download.name)

                            updated_count += 1

                    except Exception as e:
                        logger.error(f"Error updating download {download.id}: {e}")

            db.commit()

            if updated_count > 0:
                logger.debug(f"Updated {updated_count} downloads")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error in download status update task: {e}")


async def process_completed_downloads():
    """
    Process completed downloads (renaming, uploading, etc.)
    """
    try:
        logger.debug("Processing completed downloads...")

        db = SessionLocal()
        try:
            # Get completed downloads that haven't been processed
            completed_downloads = db.query(Download).filter(
                Download.status == "completed",
                Download.renamed == False
            ).all()

            processed_count = 0

            for download in completed_downloads:
                try:
                    # TODO: Implement file renaming logic
                    if download.auto_rename:
                        await _rename_download_files(download)

                    # TODO: Implement cloud upload logic
                    if download.auto_upload:
                        await _upload_download_files(download)

                    processed_count += 1

                except Exception as e:
                    logger.error(f"Error processing download {download.id}: {e}")

            db.commit()

            if processed_count > 0:
                logger.info(f"Processed {processed_count} completed downloads")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error in completed downloads processing task: {e}")


async def _rename_download_files(download: Download):
    """
    Rename download files based on TMDB metadata
    """
    try:
        # TODO: Implement TMDB-based renaming
        # This is a placeholder for the renaming logic
        logger.info(f"Renaming files for download: {download.name}")

        # Mark as renamed
        download.renamed = True

    except Exception as e:
        logger.error(f"Error renaming download {download.id}: {e}")


async def _upload_download_files(download: Download):
    """
    Upload download files to cloud storage
    """
    try:
        # TODO: Implement cloud upload logic
        # This is a placeholder for the upload logic
        logger.info(f"Uploading files for download: {download.name}")

        # Mark as uploaded
        download.uploaded = True

    except Exception as e:
        logger.error(f"Error uploading download {download.id}: {e}")


async def retry_failed_downloads():
    """
    Retry failed downloads
    """
    try:
        logger.info("Retrying failed downloads...")

        db = SessionLocal()
        try:
            # Get failed downloads with retry count < 3
            failed_downloads = db.query(Download).filter(
                Download.status == "failed",
                Download.retry_count < 3
            ).all()

            retried_count = 0

            for download in failed_downloads:
                try:
                    # Retry adding to download client
                    client_id = await qb_downloader.add_download(
                        download.download_url,
                        category="MediPaka-Retry"
                    )

                    if client_id:
                        download.client_id = client_id
                        download.status = "downloading"
                        download.retry_count += 1
                        download.error_message = None

                        logger.info(f"Retried download: {download.name}")
                        retried_count += 1
                    else:
                        download.retry_count += 1
                        download.error_message = "Failed to add to download client (retry)"

                except Exception as e:
                    download.retry_count += 1
                    download.error_message = str(e)
                    logger.error(f"Error retrying download {download.id}: {e}")

            db.commit()

            if retried_count > 0:
                logger.info(f"Retried {retried_count} failed downloads")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error in retry failed downloads task: {e}")
