"""
安全中间件
处理安全相关的中间件配置
"""
from typing import Callable

from fastapi import FastAPI, Request
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from .auth import AuthMiddleware

from backend.utils.config import settings


async def security_headers_dispatch(request: Request, call_next: Callable):
    """添加安全响应头"""
    response = await call_next(request)

    # 安全响应头
    security_headers = {
        # 防止点击劫持
        "X-Frame-Options": "DENY",
        # 防止MIME类型嗅探
        "X-Content-Type-Options": "nosniff",
        # XSS保护
        "X-XSS-Protection": "1; mode=block",
        # 引用策略
        "Referrer-Policy": "strict-origin-when-cross-origin",
    }

    # 仅在HTTPS环境下添加HSTS
    if settings.is_production():
        security_headers.update({
            # HTTP严格传输安全
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            # 内容安全策略（根据需要调整）
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com; img-src 'self' data: https:;",
        })

    # 添加安全头
    for header, value in security_headers.items():
        response.headers[header] = value

    return response


def setup_security_middleware(app: FastAPI) -> None:
    """设置所有安全相关中间件"""

    app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
    app.add_middleware(BaseHTTPMiddleware, dispatch=security_headers_dispatch)

    # 添加认证中间件
    app.add_middleware(AuthMiddleware)
