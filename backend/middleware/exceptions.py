"""
异常处理中间件
统一处理应用中的异常
"""

import time
from typing import Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse
from loguru import logger as logger

from backend.schemas import ErrorResponse
from backend.utils.config import settings


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理"""
    request_id = getattr(request.state, 'request_id', 'unknown')

    # 记录HTTP异常（除了404和401，这些比较常见）
    if exc.status_code not in [404, 401]:
        logger.warning(
            f"[{request_id}] HTTP异常: {exc.status_code} - {exc.detail} "
            f"路径: {request.method} {request.url.path}"
        )

    error_response = ErrorResponse(request_id=request_id, code=exc.status_code, details=exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理"""
    request_id = getattr(request.state, 'request_id', 'unknown')

    # 记录验证错误
    logger.warning(
        f"[{request_id}] 请求验证失败: {request.method} {request.url.path} "
        f"错误: {exc.errors()}"
    )

    error_response = ErrorResponse(
        request_id=request_id,
        code=422,
        message="请求参数验证失败",
        details=exc.errors()
    )
    return JSONResponse(
        status_code=422,
        content=error_response.model_dump()
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理"""
    request_id = getattr(request.state, 'request_id', 'unknown')

    # 记录未处理的异常
    logger.error(
        f"[{request_id}] 未处理的异常: {type(exc).__name__}: {str(exc)} "
        f"路径: {request.method} {request.url.path}",
        exc_info=True
    )

    # 生产环境隐藏详细错误信息
    if settings.is_production():
        message = "服务器内部错误"
        details = None
    else:
        message = f"{type(exc).__name__}: {str(exc)}"
        details = {
            "type": type(exc).__name__,
            "message": str(exc),
        }

    error_response = ErrorResponse(
        request_id=request_id,
        code=500,
        message=message,
        details=details
    )
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )

async def sqlalchemy_exception_handler(request: Request, exc) -> JSONResponse:
    """SQLAlchemy异常处理"""
    request_id = getattr(request.state, 'request_id', 'unknown')

    logger.error(
        f"[{request_id}] 数据库异常: {type(exc).__name__}: {str(exc)} "
        f"路径: {request.method} {request.url.path}",
        exc_info=True
    )

    # 生产环境隐藏数据库错误详情
    if settings.is_production():
        message = "数据库操作失败"
        details = None
    else:
        message = f"数据库错误: {str(exc)}"
        details = {
            "type": type(exc).__name__,
            "message": str(exc),
        }

    error_response = ErrorResponse(
        request_id=request_id,
        code=500,
        message=message,
        details=details
    )
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )


def add_custom_exception_handlers(app: FastAPI) -> None:
    """添加自定义异常处理器"""

    # 可以在这里添加更多自定义异常处理
    # 例如：数据库异常、认证异常等

    from sqlalchemy.exc import SQLAlchemyError
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)


def setup_exception_handlers(app: FastAPI) -> None:
    """设置所有异常处理器"""

    # 添加基本异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # 添加自定义异常处理器
    add_custom_exception_handlers(app)
