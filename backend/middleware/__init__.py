"""
中间件模块
提供全局中间件功能和统一管理
"""

# 认证中间件
from .auth import (
    AuthMiddleware,
    get_current_user_from_request,
    get_user_id_from_request,
    get_username_from_request
)
# CORS中间件
from .cors import (
    setup_cors_middleware
)
# 异常处理中间件
from .exceptions import (
    setup_exception_handlers,
)
# 日志中间件和日志系统
from .logging import (
    setup_logging_middleware,
    log_performance,
    log_request
)
# 安全中间件
from .security import (
    setup_security_middleware
)

__all__ = [
    # 认证相关
    "AuthMiddleware",
    "get_current_user_from_request",
    "get_user_id_from_request",
    "get_username_from_request",

    # CORS相关
    "setup_cors_middleware",

    # 安全相关
    "setup_security_middleware",

    # 日志相关
    "setup_logging_middleware",
    "log_request",
    "log_performance",

    # 异常处理相关
    "setup_exception_handlers",
]
