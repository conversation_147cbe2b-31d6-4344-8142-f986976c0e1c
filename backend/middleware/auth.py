"""
全局鉴权中间件
自动为需要保护的API路径添加鉴权验证
"""

import re
from typing import List, Optional

from fastapi import Request, status, FastAPI
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from backend.services import auth_service


class AuthMiddleware(BaseHTTPMiddleware):
    """全局鉴权中间件"""

    def __init__(self, app, exclude_paths: Optional[List[str]] = None):
        super().__init__(app)

        # 不需要鉴权的路径（支持正则表达式）
        self.exclude_patterns = [
            r"^/$",  # 根路径
            r"^/docs.*",  # API文档
            r"^/redoc.*",  # ReDoc文档
            r"^/openapi\.json$",  # OpenAPI规范
            r"^/health$",  # 健康检查
            r"^/info$",  # 系统信息
            r"^/api/auth/login$",  # 登录接口
            r"^/static/.*",  # 静态文件
            r"^/favicon\.ico$",  # 网站图标
        ]

        # 添加用户自定义的排除路径
        if exclude_paths:
            self.exclude_patterns.extend(exclude_paths)

        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern) for pattern in self.exclude_patterns]

    def is_excluded_path(self, path: str) -> bool:
        """检查路径是否在排除列表中"""
        for pattern in self.compiled_patterns:
            if pattern.match(path):
                return True
        return False

    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        path = request.url.path

        # 跳过OPTIONS请求（CORS预检请求）
        if request.method == "OPTIONS":
            return await call_next(request)

        # 检查是否需要跳过鉴权
        if self.is_excluded_path(path):
            return await call_next(request)

        # 检查是否为API路径
        if not path.startswith("/api/"):
            return await call_next(request)

        # 获取Authorization头
        authorization = request.headers.get("Authorization")
        if not authorization:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "success": False,
                    "code": 401,
                    "message": "未提供认证令牌",
                    "timestamp": int(__import__('time').time())
                }
            )

        # 检查Bearer格式
        if not authorization.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "success": False,
                    "code": 401,
                    "message": "认证令牌格式错误",
                    "timestamp": int(__import__('time').time())
                }
            )

        # 提取令牌
        token = authorization[7:]  # 移除 "Bearer " 前缀

        # 验证令牌
        token_data = auth_service.verify_token(token)
        if not token_data:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "success": False,
                    "code": 401,
                    "message": "无效的认证令牌",
                    "timestamp": int(__import__('time').time())
                }
            )

        # 获取用户信息
        username = token_data.get("sub")
        if not username:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "success": False,
                    "code": 401,
                    "message": "令牌中缺少用户信息",
                    "timestamp": int(__import__('time').time())
                }
            )

        # 验证用户是否存在且激活
        from backend.database import SessionLocal
        db = SessionLocal()
        try:
            user = auth_service.get_user_by_username(db, username)
            if not user:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={
                        "success": False,
                        "code": 401,
                        "message": "用户不存在",
                        "timestamp": int(__import__('time').time())
                    }
                )

            # 检查令牌是否在用户登出之后签发
            token_iat = token_data.get("iat")
            if token_iat and user.logout_time:
                if token_iat < user.logout_time.timestamp():
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "success": False,
                            "code": 401,
                            "message": "令牌已失效，请重新登录",
                            "timestamp": int(__import__('time').time())
                        }
                    )

            # 将用户信息添加到请求状态中
            request.state.current_user = user
            request.state.user_id = user.id
            request.state.username = user.username

        finally:
            db.close()

        # 继续处理请求
        return await call_next(request)


def get_current_user_from_request(request: Request):
    """从请求中获取当前用户"""
    return getattr(request.state, 'current_user', None)


def get_user_id_from_request(request: Request) -> Optional[int]:
    """从请求中获取用户ID"""
    return getattr(request.state, 'user_id', None)


def get_username_from_request(request: Request) -> Optional[str]:
    """从请求中获取用户名"""
    return getattr(request.state, 'username', None)


def setup_auth_middleware(app: FastAPI) -> None:
    """设置认证中间件"""
    app.add_middleware(AuthMiddleware)
