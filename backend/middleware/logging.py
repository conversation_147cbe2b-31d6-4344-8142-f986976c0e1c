"""
日志中间件
处理请求日志记录和性能监控
"""
import logging
import sys
import time
import uuid
from pathlib import Path
from typing import Callable, Optional

from fastapi import FastAPI, Request
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware

from backend.utils.config import settings


class InterceptHandler(logging.Handler):
    def emit(self, record):
        # 过滤掉不需要的日志
        if record.name.startswith(('sqlalchemy', 'uvicorn.access')):
            return

        # 获取对应的loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        # 只在开发环境或错误级别时记录
        if settings.is_development() or record.levelno >= logging.ERROR:
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )


# 日志过滤器
def _log_filter(record):
    """过滤不需要的日志"""

    # 过滤SQLAlchemy和其他噪音日志
    excluded_modules = [
        'sqlalchemy',
        'uvicorn.access',
        'charset_normalizer',
        'urllib3.connectionpool'
    ]

    for module in excluded_modules:
        if record["name"].startswith(module):
            return False

    return True


def setup_logging():
    # 移除所有默认的处理器
    logging.getLogger().handlers = []
    # 移除默认的loguru处理器
    logger.remove()
    # 创建日志目录
    log_dir = Path(settings.logs_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    # 控制台日志配置
    if settings.is_development():
        # 开发环境：彩色输出，详细信息
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        console_level = settings.log_level
    else:
        # 生产环境：简洁输出
        console_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        console_level = "INFO"

    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=console_level,
        colorize=settings.is_development(),
        backtrace=settings.is_development(),
        diagnose=settings.is_development(),
        filter=_log_filter
    )

    # 文件日志配置
    if settings.logs_path:
        # 主日志文件
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{process} | "
            "{message}"
        )

        logger.add(
            settings.logs_path,
            format=file_format,
            level="DEBUG",
            rotation=settings.log_max_size,
            retention=settings.log_backup_count,
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )

        # 生产环境额外的错误日志文件
        if settings.is_production():
            error_log_file = str(Path(settings.LOG_FILE).with_suffix('.error.log'))
            logger.add(
                error_log_file,
                format=file_format,
                level="ERROR",
                rotation=settings.LOG_MAX_SIZE,
                retention=settings.LOG_BACKUP_COUNT,
                compression="zip",
                encoding="utf-8",
                backtrace=True,
                diagnose=True
            )

    # 设置第三方库日志级别
    if settings.is_development():
        # 开发环境：显示重要信息，但减少噪音
        third_party_loggers = {
            'uvicorn': logging.WARNING,
            'uvicorn.access': logging.CRITICAL,  # 完全禁用
            'uvicorn.error': logging.WARNING,
            'fastapi': logging.WARNING,
            'sqlalchemy': logging.CRITICAL,  # 完全禁用SQL日志
            'sqlalchemy.engine': logging.CRITICAL,
            'sqlalchemy.pool': logging.CRITICAL,
            'sqlalchemy.dialects': logging.CRITICAL,
            'httpx': logging.ERROR,
            'httpcore': logging.ERROR,
            'asyncio': logging.ERROR,
            'multipart': logging.ERROR,
            'charset_normalizer': logging.CRITICAL,
            'urllib3': logging.ERROR,
            'requests': logging.ERROR,
        }
    else:
        # 生产环境：只显示错误
        third_party_loggers = {
            'uvicorn': logging.ERROR,
            'uvicorn.access': logging.CRITICAL,
            'uvicorn.error': logging.ERROR,
            'fastapi': logging.ERROR,
            'sqlalchemy': logging.CRITICAL,
            'sqlalchemy.engine': logging.CRITICAL,
            'sqlalchemy.pool': logging.CRITICAL,
            'sqlalchemy.dialects': logging.CRITICAL,
            'httpx': logging.CRITICAL,
            'httpcore': logging.CRITICAL,
            'asyncio': logging.CRITICAL,
            'multipart': logging.CRITICAL,
            'charset_normalizer': logging.CRITICAL,
            'urllib3': logging.CRITICAL,
            'requests': logging.CRITICAL,
        }

    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)
        # 确保禁用传播
        logging.getLogger(logger_name).propagate = False

    # 获取根日志器并清除所有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.addHandler(InterceptHandler())
    root_logger.setLevel(logging.DEBUG)

    # 确保所有日志器都使用我们的处理器
    for name in logging.root.manager.loggerDict:
        logging.getLogger(name).handlers.clear()
        logging.getLogger(name).propagate = True


async def log_request(request: Request, status_code: int, duration: float,
                      request_id: str, error: Optional[str] = None) -> None:
    """记录HTTP请求日志"""

    message = f"[{request_id}] {request.method} {request.url.path} - {status_code} - {duration:.3f}s"
    if error:
        message += f" | ERROR: {error}"

    # 根据状态码选择日志级别并记录
    log_func = logger.error if status_code >= 500 else logger.warning if status_code >= 400 else logger.info
    log_func(message)


def log_performance(operation: str, duration: float, **kwargs) -> None:
    """记录性能日志"""
    perf_logger = logger.bind(component="performance")

    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
    message = f"Performance: {operation} took {duration:.3f}s"

    if extra_info:
        message += f" | {extra_info}"

    if duration > 1.0:  # 超过1秒的操作记录为WARNING
        perf_logger.warning(message)
    elif settings.is_development():
        perf_logger.info(message)


async def request_logging_dispatch(request: Request, call_next: Callable):
    """记录HTTP请求日志和性能指标，包含请求参数"""

    # 生成请求ID
    request_id = str(uuid.uuid4())[:8]
    request.state.request_id = request_id

    start_time = time.time()

    try:
        response = await call_next(request)
        duration = time.time() - start_time

        # 记录请求日志（使用logging模块的函数）
        await log_request(request, response.status_code, duration, request_id)

        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Response-Time"] = f"{duration:.3f}s"

        return response

    except Exception as e:
        duration = time.time() - start_time

        # 记录错误日志
        await log_request(request, 500, duration, request_id, str(e))
        raise


async def performance_dispatch(request: Request, call_next: Callable):
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time

    # 添加性能相关的响应头
    response.headers["X-Process-Time"] = str(process_time)

    # 如果响应时间过长，记录警告
    if process_time > 1.0:  # 超过1秒
        logger.warning(
            f"慢请求警告: {request.method} {request.url.path} "
            f"耗时 {process_time:.3f}s"
        )
    elif process_time > 5.0:  # 超过5秒
        logger.error(
            f"极慢请求: {request.method} {request.url.path} "
            f"耗时 {process_time:.3f}s"
        )

    return response


def setup_logging_middleware(app: FastAPI) -> None:
    """设置所有日志相关中间件"""

    # 初始化日志系统
    setup_logging()

    # 添加请求日志中间件
    app.add_middleware(BaseHTTPMiddleware, dispatch=request_logging_dispatch)

    # 添加性能监控中间件
    app.add_middleware(BaseHTTPMiddleware, dispatch=performance_dispatch)
