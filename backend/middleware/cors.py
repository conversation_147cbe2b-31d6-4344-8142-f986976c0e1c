"""
CORS中间件配置
处理跨域资源共享
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


def add_cors_middleware(app: FastAPI) -> None:
    """添加CORS中间件"""

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=[
            "X-Request-ID",
            "X-Response-Time",
        ],
        max_age=600,  # 预检请求缓存时间（秒）
    )


def setup_cors_middleware(app: FastAPI) -> None:
    add_cors_middleware(app)
