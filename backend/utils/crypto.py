"""
加密工具
提供密码加密、JWT令牌等功能
"""

import hashlib
import secrets
import base64
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any

from jose import JWTError, jwt

from backend.utils.config import settings


def _pbkdf2_hash(password: str, salt: bytes, iterations: int = 100000) -> bytes:
    """使用PBKDF2算法哈希密码"""
    return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, iterations)


def hash_password(password: str) -> str:
    """
    加密密码
    使用PBKDF2-SHA256算法，比bcrypt更稳定且无依赖问题
    """
    # 生成随机盐
    salt = secrets.token_bytes(32)
    # 使用PBKDF2哈希密码
    hashed = _pbkdf2_hash(password, salt)
    # 组合盐和哈希值，使用base64编码
    combined = salt + hashed
    return base64.b64encode(combined).decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    """
    try:
        # 解码base64
        combined = base64.b64decode(hashed_password.encode('utf-8'))
        # 提取盐（前32字节）和哈希值（后32字节）
        salt = combined[:32]
        stored_hash = combined[32:]
        # 使用相同的盐哈希输入密码
        computed_hash = _pbkdf2_hash(plain_password, salt)
        # 比较哈希值
        return secrets.compare_digest(stored_hash, computed_hash)
    except Exception:
        return False


def generate_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """生成JWT令牌"""
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.security_jwt_expire_minutes)

    to_encode.update({"exp": expire})

    # 获取JWT密钥的实际值
    jwt_secret = settings.security_jwt_secret_key
    if hasattr(jwt_secret, 'get_secret_value'):
        jwt_secret_value = jwt_secret.get_secret_value()
    else:
        jwt_secret_value = jwt_secret

    encoded_jwt = jwt.encode(to_encode, jwt_secret_value, algorithm=settings.security_jwt_algorithm)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT令牌"""
    try:
        # 获取JWT密钥的实际值
        jwt_secret = settings.security_jwt_secret_key
        if hasattr(jwt_secret, 'get_secret_value'):
            jwt_secret_value = jwt_secret.get_secret_value()
        else:
            jwt_secret_value = jwt_secret

        payload = jwt.decode(token, jwt_secret_value, algorithms=[settings.security_jwt_algorithm])
        return payload
    except JWTError:
        return None


def generate_random_string(length: int = 32) -> str:
    """生成随机字符串"""
    return secrets.token_urlsafe(length)


def generate_api_key() -> str:
    """生成API密钥"""
    return f"mk_{generate_random_string(32)}"


def hash_string(text: str) -> str:
    """计算字符串哈希值"""
    return hashlib.sha256(text.encode()).hexdigest()


def generate_file_hash(file_path: str) -> str:
    """计算文件哈希值"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception:
        return ""


def generate_jwt_secret_key() -> str:
    """生成JWT密钥"""
    return secrets.token_urlsafe(64)


def generate_secret_key() -> str:
    """生成通用密钥"""
    return secrets.token_urlsafe(32)


def rotate_jwt_keys() -> Dict[str, str]:
    """轮换JWT密钥，返回新的密钥对"""
    return {
        "secret_key": generate_secret_key(),
        "jwt_secret_key": generate_jwt_secret_key()
    }


def validate_jwt_secret_key(key: str) -> bool:
    """验证JWT密钥格式是否有效"""
    if not key or len(key) < 32:
        return False
    try:
        # 尝试使用密钥创建一个测试token
        test_data = {"test": "validation"}
        test_token = jwt.encode(test_data, key, algorithm="HS256")
        decoded = jwt.decode(test_token, key, algorithms=["HS256"])
        return decoded.get("test") == "validation"
    except Exception:
        return False


def create_jwt_token_with_custom_key(data: Dict[str, Any], secret_key: str,
                                     expires_delta: Optional[timedelta] = None) -> str:
    """使用自定义密钥生成JWT令牌"""
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.JWT_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=settings.security_jwt_algorithm)
    return encoded_jwt


def verify_jwt_token_with_custom_key(token: str, secret_key: str) -> Optional[Dict[str, Any]]:
    """使用自定义密钥验证JWT令牌"""
    try:
        payload = jwt.decode(token, secret_key, algorithms=[settings.security_jwt_algorithm])
        return payload
    except JWTError:
        return None
