"""
时间工具
提供时间处理相关的工具函数
"""

import time
from datetime import datetime, timezone, timedelta
from typing import Optional, Union


def now_utc() -> datetime:
    """获取当前UTC时间"""
    return datetime.now(timezone.utc)


def get_timestamp() -> int:
    """获取当前时间戳"""
    return int(time.time())


def format_datetime(dt: Optional[datetime], fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间"""
    if dt is None:
        return ""
    return dt.strftime(fmt)


def parse_datetime(date_string: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """解析时间字符串"""
    try:
        return datetime.strptime(date_string, fmt)
    except (ValueError, TypeError):
        return None


def to_utc(dt: datetime) -> datetime:
    """转换为UTC时间"""
    if dt.tzinfo is None:
        # 假设是本地时间
        return dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)


def to_local(dt: datetime) -> datetime:
    """转换为本地时间"""
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone()


def days_ago(days: int) -> datetime:
    """获取几天前的时间"""
    return now_utc() - timedelta(days=days)


def hours_ago(hours: int) -> datetime:
    """获取几小时前的时间"""
    return now_utc() - timedelta(hours=hours)


def minutes_ago(minutes: int) -> datetime:
    """获取几分钟前的时间"""
    return now_utc() - timedelta(minutes=minutes)


def is_expired(dt: datetime) -> bool:
    """检查时间是否已过期"""
    return to_utc(dt) < now_utc()


def time_until_expire(dt: datetime) -> timedelta:
    """计算距离过期的时间"""
    return to_utc(dt) - now_utc()


def format_duration(seconds: Union[int, float]) -> str:
    """格式化时长"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
    else:
        days = seconds / 86400
        return f"{days:.1f}天"


def format_relative_time(dt: datetime) -> str:
    """格式化相对时间"""
    now = now_utc()
    diff = now - to_utc(dt)

    if diff.total_seconds() < 60:
        return "刚刚"
    elif diff.total_seconds() < 3600:
        minutes = int(diff.total_seconds() / 60)
        return f"{minutes}分钟前"
    elif diff.total_seconds() < 86400:
        hours = int(diff.total_seconds() / 3600)
        return f"{hours}小时前"
    elif diff.days < 30:
        return f"{diff.days}天前"
    else:
        return format_datetime(dt, "%Y-%m-%d")


def get_date_range(start_date: str, end_date: str) -> tuple[datetime, datetime]:
    """获取日期范围"""
    start = parse_datetime(start_date, "%Y-%m-%d")
    end = parse_datetime(end_date, "%Y-%m-%d")

    if start is None:
        start = days_ago(30)
    if end is None:
        end = now_utc()

    # 设置时间范围
    start = start.replace(hour=0, minute=0, second=0, microsecond=0)
    end = end.replace(hour=23, minute=59, second=59, microsecond=999999)

    return start, end
