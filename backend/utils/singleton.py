"""
单例模式基类
确保每个类在整个应用程序中只有一个实例
"""

import threading
from typing import Dict, Any


class SingletonMeta(type):
    """
    单例元类
    线程安全的单例实现
    """
    _instances: Dict[Any, Any] = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        """
        控制实例创建
        """
        # 双重检查锁定模式
        if cls not in cls._instances:
            with cls._lock:
                # 再次检查，防止在等待锁的过程中其他线程已经创建了实例
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]


class Singleton(metaclass=SingletonMeta):
    """
    单例基类
    继承此类的所有类都将成为单例
    """
    pass


def singleton(cls):
    """
    单例装饰器
    可以用作装饰器来创建单例类
    """
    instances = {}
    lock = threading.Lock()

    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance
