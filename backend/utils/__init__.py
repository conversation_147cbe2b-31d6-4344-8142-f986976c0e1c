"""
工具模块
提供常用的工具函数和类
"""

from .crypto import (
    hash_password,
    verify_password,
    generate_token,
    verify_token,
    generate_jwt_secret_key,
    generate_secret_key,
    rotate_jwt_keys,
    validate_jwt_secret_key
)
from .datetime import (
    now_utc,
    format_datetime,
    parse_datetime,
    get_timestamp,
    days_ago,
    hours_ago
)

from .singleton import (
    Singleton,
    SingletonMeta,
    singleton
)
from .validators import (
    validate_email,
    validate_username,
    validate_password,
    sanitize_filename
)
from .config import (
    settings,
    init_config
)

__all__ = [
    # 加密相关
    "verify_token",
    "hash_password",
    "verify_password",
    "generate_token",
    "generate_jwt_secret_key",
    "generate_secret_key",
    "rotate_jwt_keys",
    "validate_jwt_secret_key",

    # 时间相关
    "now_utc",
    "format_datetime",
    "parse_datetime",
    "get_timestamp",
    "days_ago",
    "hours_ago",

    # 验证相关
    "validate_email",
    "validate_username",
    "validate_password",
    "sanitize_filename",

    # 单例相关
    "Singleton",
    "SingletonMeta",

    "settings",
    "init_config"
]
