"""
验证工具
提供数据验证相关的工具函数
"""

import os
import re


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False

    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_username(username: str) -> tuple[bool, str]:
    """验证用户名"""
    if not username:
        return False, "用户名不能为空"

    if len(username) < 3:
        return False, "用户名至少3个字符"

    if len(username) > 50:
        return False, "用户名不能超过50个字符"

    # 只允许字母、数字、下划线、中划线
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return False, "用户名只能包含字母、数字、下划线和中划线"

    return True, ""


def validate_password(password: str) -> tuple[bool, str]:
    """验证密码强度"""
    if not password:
        return False, "密码不能为空"

    if len(password) < 6:
        return False, "密码至少6个字符"

    if len(password) > 128:
        return False, "密码不能超过128个字符"

    # 简单的密码强度检查
    has_letter = bool(re.search(r'[a-zA-Z]', password))
    has_digit = bool(re.search(r'\d', password))

    if not (has_letter and has_digit):
        return False, "密码必须包含字母和数字"

    return True, ""


def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    if not filename:
        return "untitled"

    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)

    # 移除控制字符
    filename = ''.join(char for char in filename if ord(char) >= 32)

    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        max_name_len = 255 - len(ext)
        filename = name[:max_name_len] + ext

    # 移除首尾空格和点
    filename = filename.strip(' .')

    # 如果为空，使用默认名称
    if not filename:
        filename = "untitled"

    return filename


def validate_url(url: str) -> bool:
    """验证URL格式"""
    if not url:
        return False

    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(pattern, url))


def validate_port(port: int) -> bool:
    """验证端口号"""
    return 1 <= port <= 65535


def validate_ip_address(ip: str) -> bool:
    """验证IP地址"""
    if not ip:
        return False

    # IPv4
    ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(ipv4_pattern, ip):
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)

    # IPv6 (简单检查)
    ipv6_pattern = r'^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$'
    return bool(re.match(ipv6_pattern, ip))


def validate_file_size(size: int, max_size: int = 100 * 1024 * 1024) -> tuple[bool, str]:
    """验证文件大小"""
    if size < 0:
        return False, "文件大小不能为负数"

    if size > max_size:
        max_mb = max_size / (1024 * 1024)
        return False, f"文件大小不能超过{max_mb:.1f}MB"

    return True, ""


def validate_file_extension(filename: str, allowed_extensions: list[str]) -> tuple[bool, str]:
    """验证文件扩展名"""
    if not filename:
        return False, "文件名不能为空"

    _, ext = os.path.splitext(filename.lower())
    ext = ext.lstrip('.')

    if not ext:
        return False, "文件必须有扩展名"

    if ext not in [e.lower() for e in allowed_extensions]:
        return False, f"不支持的文件类型，允许的类型: {', '.join(allowed_extensions)}"

    return True, ""


def is_safe_path(path: str, base_path: str) -> bool:
    """检查路径是否安全（防止路径遍历攻击）"""
    try:
        abs_base = os.path.abspath(base_path)
        abs_path = os.path.abspath(os.path.join(base_path, path))
        return abs_path.startswith(abs_base)
    except Exception:
        return False
