"""
优化精简的统一配置管理
支持环境变量覆盖YAML配置，优先级：env > yaml
"""

import os
import secrets
from enum import Enum
from pathlib import Path
from typing import Dict, Any

import yaml
from loguru import logger
from pydantic import Field
from pydantic.types import SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "dev"
    PRODUCTION = "prod"


class YamlConfigSource:
    """YAML配置文件数据源"""

    def __init__(self, settings_cls):
        self.settings_cls = settings_cls

    def _get_config_file(self) -> str:
        """获取配置文件路径"""
        env = os.getenv("ENV", "dev").lower()
        config_dir = Path("data")
        config_dir.mkdir(exist_ok=True)
        return str(config_dir / f"config.{env}.yaml")

    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        config_file = self._get_config_file()
        config_path = Path(config_file)

        if not config_path.exists():
            logger.info(f"配置文件 {config_file} 不存在，使用默认配置")
            return {}

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            return self._flatten_config(config)
        except Exception as e:
            return {}

    def _flatten_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """将嵌套的YAML配置扁平化为环境变量格式"""
        flattened = {}

        # 应用配置
        if 'app' in config:
            app = config['app']
            if 'debug' in app:
                flattened['debug'] = app['debug']
            if 'environment' in app:
                flattened['environment'] = app['environment']

        # 服务器配置
        if 'server' in config:
            server = config['server']
            for key, value in server.items():
                flattened[f'server_{key}'] = value

        # 数据库配置
        if 'database' in config:
            db = config['database']
            for key, value in db.items():
                if key == 'url':
                    flattened['database_url'] = value
                else:
                    flattened[f'database_{key}'] = value

        # 安全配置
        if 'security' in config:
            security = config['security']
            for key, value in security.items():
                flattened[f'security_{key}'] = value

        # 日志配置
        if 'logging' in config:
            logging = config['logging']
            for key, value in logging.items():
                if key == 'level':
                    flattened['log_level'] = value
                elif key == 'max_size':
                    flattened['log_max_size'] = value
                elif key == 'backup_count':
                    flattened['log_backup_count'] = value

        # 路径配置
        if 'paths' in config:
            paths = config['paths']
            for key, value in paths.items():
                if key == 'data':
                    flattened['data_dir'] = value
                elif key == 'logs':
                    flattened['logs_path'] = value
                else:
                    flattened[f'{key}_path'] = value

        # 外部服务配置
        if 'services' in config:
            services = config['services']
            for service_name, service_config in services.items():
                for key, value in service_config.items():
                    flattened[f'{service_name}_{key}'] = value

        # 性能配置
        if 'performance' in config:
            perf = config['performance']
            for key, value in perf.items():
                flattened[key] = value

        # 监控配置
        if 'monitor' in config:
            monitor = config['monitor']
            for key, value in monitor.items():
                flattened[key] = value

        return flattened

    def __call__(self) -> Dict[str, Any]:
        """返回YAML配置数据"""
        return self._load_config()


class Settings(BaseSettings):
    """优化精简的统一配置类 - 所有配置属性集中管理"""

    # 应用基本信息
    debug: bool = Field(default=False, description="调试模式")
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="运行环境")

    # 服务器配置
    server_host: str = Field(default="0.0.0.0", description="服务器地址")
    server_port: int = Field(default=8000, description="服务器端口")
    server_workers: int = Field(default=1, description="工作进程数")

    # 数据库配置
    database_url: str = Field(default="sqlite:///./data/medipaka.db", description="数据库连接URL")
    database_echo: bool = Field(default=False, description="是否打印SQL语句")
    database_pool_size: int = Field(default=10, description="连接池大小")
    database_max_overflow: int = Field(default=20, description="连接池最大溢出")

    # 安全配置
    security_secret_key: SecretStr = Field(default_factory=lambda: SecretStr(secrets.token_urlsafe(32)),
                                           description="应用密钥")
    security_jwt_secret_key: SecretStr = Field(default_factory=lambda: SecretStr(secrets.token_urlsafe(64)),
                                               description="JWT密钥")
    security_jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    security_jwt_expire_minutes: int = Field(default=1440, description="JWT过期时间(分钟)")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_max_size: str = Field(default="10MB", description="日志文件最大大小")
    log_backup_count: int = Field(default=5, description="日志文件备份数量")

    # 路径配置
    data_dir: str = Field(default="./data", description="数据目录")
    logs_path: str = Field(default="./data/logs/medipaka.log", description="日志文件路径")
    rules_path: str = Field(default="./data/rules", description="规则目录")
    downloads_path: str = Field(default="./data/downloads", description="下载目录")
    media_path: str = Field(default="./data/media", description="媒体目录")
    temp_path: str = Field(default="./data/tmp", description="临时目录")

    # TMDB配置
    tmdb_api_key: str = Field(default="", description="TMDB API密钥")
    tmdb_language: str = Field(default="zh-CN", description="TMDB语言")
    tmdb_region: str = Field(default="CN", description="TMDB地区")

    # Telegram配置
    telegram_bot_token: str = Field(default="", description="Telegram Bot Token")
    telegram_chat_id: str = Field(default="", description="Telegram聊天ID")
    telegram_enabled: bool = Field(default=False, description="是否启用Telegram")

    # qBittorrent配置
    qbittorrent_host: str = Field(default="localhost", description="qBittorrent主机地址")
    qbittorrent_port: int = Field(default=8080, description="qBittorrent端口")
    qbittorrent_username: str = Field(default="admin", description="qBittorrent用户名")
    qbittorrent_password: str = Field(default="", description="qBittorrent密码")
    qbittorrent_enabled: bool = Field(default=False, description="是否启用qBittorrent")

    # 性能配置
    max_concurrent_downloads: int = Field(default=5, description="最大并发下载数")
    download_retry_times: int = Field(default=3, description="下载重试次数")
    download_timeout: int = Field(default=3600, description="下载超时时间")

    # 监控配置
    subscription_check_interval: int = Field(default=3600, description="订阅检查间隔")
    monitor_interval: int = Field(default=300, description="监控间隔")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        # 环境变量前缀映射
        env_prefix="",
        # 支持嵌套环境变量分隔符
        env_nested_delimiter="__"
    )

    @classmethod
    def settings_customise_sources(
            cls,
            settings_cls,
            init_settings,
            env_settings,
            dotenv_settings,
            file_secret_settings
    ):
        """自定义配置源优先级：环境变量 > YAML文件 > 默认值"""
        return (
            init_settings,
            env_settings,
            dotenv_settings,
            YamlConfigSource(settings_cls),
            file_secret_settings,
        )

    def __init__(self, **kwargs):
        """初始化配置"""
        super().__init__(**kwargs)

    def save_config(self, file_path: str):
        """保存配置到YAML文件"""
        config_dict = self._get_yaml_config()

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2, sort_keys=False)
            logger.info(f"配置已保存到: {file_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")

    def default_config(self) -> Dict[str, Any]:
        """获取YAML格式的配置字典"""
        env = self.environment.value

        return {
            # 应用配置
            "app": {
                "debug": self.debug,
                "environment": env,
                "server_host": self.server_host,
                "server_port": self.server_port,
            },
            # 数据库配置
            "database": {
                "url": f"sqlite:///./data/medipaka_{env}.db",
                "echo": self.database_echo,
                "pool_size": self.database_pool_size,
                "max_overflow": self.database_max_overflow
            },

            # 安全配置
            "security": {
                "secret_key": self.security_secret_key.get_secret_value(),
                "jwt_secret_key": self.security_jwt_secret_key.get_secret_value(),
                "jwt_algorithm": self.security_jwt_algorithm,
                "jwt_expire_minutes": self.security_jwt_expire_minutes
            },

            # 日志配置
            "logging": {
                "level": "DEBUG" if env == "dev" else "INFO",
                "max_size": self.log_max_size,
                "backup_count": self.log_backup_count
            },

            # 路径配置
            "paths": {
                "data": self.data_dir,
                "logs": self.logs_path,
                "rules": self.rules_path,
                "downloads": self.downloads_path,
                "media": self.media_path,
                "temp": self.temp_path
            },

            # 外部服务配置
            "services": {
                "tmdb": {
                    "api_key": self.tmdb_api_key,
                    "language": self.tmdb_language,
                    "region": self.tmdb_region
                },
                "telegram": {
                    "bot_token": self.telegram_bot_token,
                    "chat_id": self.telegram_chat_id,
                    "enabled": self.telegram_enabled
                },
                "qbittorrent": {
                    "host": self.qbittorrent_host,
                    "port": self.qbittorrent_port,
                    "username": self.qbittorrent_username,
                    "password": self.qbittorrent_password,
                    "enabled": self.qbittorrent_enabled
                }
            },

            # 性能配置
            "performance": {
                "max_concurrent_downloads": self.max_concurrent_downloads,
                "download_retry_times": self.download_retry_times,
                "download_timeout": self.download_timeout
            },

            # 监控配置
            "monitor": {
                "subscription_check_interval": self.subscription_check_interval,
                "monitor_interval": self.monitor_interval
            }
        }

    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值，支持点分隔的路径"""

        # 处理点分隔的路径，转换为属性名
        if '.' in key_path:
            # 兼容旧的嵌套路径格式
            parts = key_path.split('.')
            if len(parts) == 2:
                section, key = parts
                attr_name = f"{section}_{key}" if section != "app" else key
                return getattr(self, attr_name, default)

        # 直接获取属性
        return getattr(self, key_path, default)

    def update(self, updates: Dict[str, Any]) -> bool:
        """更新配置并保存到文件"""
        try:
            # 获取当前配置
            current_config = self.get_config()

            def deep_merge(base: dict, updates: dict):
                for key, value in updates.items():
                    if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                        deep_merge(base[key], value)
                    else:
                        base[key] = value

            deep_merge(current_config, updates)

            # 保存到YAML文件
            env = os.getenv("ENV", "dev").lower()
            config_file = Path("data") / f"config.{env}.yaml"

            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(current_config, f, default_flow_style=False,
                          allow_unicode=True, indent=2, sort_keys=False)

            logger.info(f"配置已更新并保存到: {config_file}")
            return True

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False

    def reload(self) -> bool:
        """重新加载配置"""
        try:
            # 重新初始化配置
            global settings
            settings = Settings()
            logger.info("配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False

    def get_environment(self) -> Environment:
        """获取当前环境"""
        return settings.environment

    def is_development(self) -> bool:
        """是否为开发环境"""
        return settings.environment == Environment.DEVELOPMENT

    def is_production(self) -> bool:
        """是否为生产环境"""
        return settings.environment == Environment.PRODUCTION


# 全局配置实例
settings = Settings()


def init_config():
    """创建必要的目录"""
    directories = [
        settings.data_dir,
        settings.rules_path,
        settings.downloads_path,
        settings.media_path,
        settings.temp_path,
        Path(settings.logs_path).parent
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

    env = os.getenv("ENV", "dev").lower()
    config_file = Path("data") / f"config.{env}.yaml"

    if not config_file.exists():
        config_dict = settings.default_config()
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2, sort_keys=False)
            logger.info(f"配置已保存到: {config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
