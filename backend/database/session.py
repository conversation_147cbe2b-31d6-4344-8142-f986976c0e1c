"""
数据库会话管理模块
负责创建和管理数据库会话、事务等
"""

from contextlib import contextmanager
from typing import Generator

from loguru import logger
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker, Session

from .connection import db_connection


class SessionManager:
    """
    数据库会话管理器
    负责创建和管理数据库会话
    """

    def __init__(self):
        self._session_factory = None

    @property
    def session_factory(self) -> sessionmaker:
        """获取会话工厂"""
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=db_connection.engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
        return self._session_factory

    def create_session(self) -> Session:
        """创建新的会话"""
        return self.session_factory()


# 全局会话管理器实例
session_manager = SessionManager()


# FastAPI 依赖注入函数
def get_db() -> Generator[Session, None, None]:
    """
    数据库会话依赖
    用于FastAPI依赖注入

    使用示例:
    @app.get("/users/")
    def read_users(db: Session = Depends(get_db)):
        return User.get_multi(db, limit=10)
    """
    db = session_manager.create_session()
    try:
        yield db
    except SQLAlchemyError as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


# 上下文管理器
@contextmanager
def db_session() -> Generator[Session, None, None]:
    """
    数据库会话上下文管理器
    用于手动管理数据库会话，自动提交事务

    使用示例:
    with db_session() as db:
        user = User.create_user(db, "test", "password")
        # 自动提交
    """
    session = session_manager.create_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库事务回滚: {e}")
        raise
    finally:
        session.close()


@contextmanager
def db_transaction() -> Generator[Session, None, None]:
    """
    数据库事务上下文管理器
    用于需要手动控制事务的场景

    使用示例:
    with db_transaction() as db:
        user = User(username="test")
        user.create(db)
        db.flush()  # 获取ID但不提交
        # 手动调用 db.commit() 或让上下文管理器自动提交
    """
    session = session_manager.create_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库事务失败: {e}")
        raise
    finally:
        session.close()
