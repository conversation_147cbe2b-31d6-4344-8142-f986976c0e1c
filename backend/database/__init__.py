"""
数据库模块
提供精简的数据库访问层
"""

# 核心功能
from .init import init_database, check_database_status
from .session import get_db, db_session

# 模型（集成了仓储操作）
from .models import *
from .models.base import Base, db_query, db_update

# 向后兼容（最小化暴露）
from .database import (
    engine,
    SessionLocal,
    metadata,
    create_default_admin,
    check_database_connection,
    get_database_info
)

__all__ = [
    # 核心功能
    "init_database",
    "check_database_status",
    "get_db",
    "db_session",

    # 装饰器
    "db_query",
    "db_update",

    # 基础类
    "Base",

    # 模型
    "User",
    "Media",
    "MediaFile",
    "Subscription",
    "SubscriptionRule",
    "Download",
    "DownloadTask",
    "Notification",
    "NotificationTemplate",

    # 向后兼容（最小化）
    "engine",
    "SessionLocal",
    "metadata",
    "create_default_admin",
    "check_database_connection",
    "get_database_info",
]
