"""
Media schemas
媒体模型，集成媒体仓储操作
"""
from enum import Enum
from typing import Optional, List, Dict, Any

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Float, and_, or_
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

from .base import BaseModel, db_query, db_update


class MediaType(str, Enum):
    """Media type"""
    MOVIE = "movie"
    TV = "tv"
    ANIME = "anime"
    DOCUMENTARY = "documentary"
    OTHER = "other"


class MediaStatus(str, Enum):
    """Media status"""
    WANTED = "wanted"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    PROCESSED = "processed"
    ARCHIVED = "archived"


class Media(BaseModel):
    """
    Media model for tracking media items
    集成媒体相关的所有数据库操作
    """
    __tablename__ = "media"

    # Basic information
    title = Column(String(500), nullable=False, index=True)
    original_title = Column(String(500), nullable=True)
    type = Column(String(50), nullable=False, default=MediaType.OTHER)
    status = Column(String(50), nullable=False, default=MediaStatus.WANTED)

    # External IDs
    tmdb_id = Column(Integer, nullable=True, index=True)
    imdb_id = Column(String(20), nullable=True, index=True)
    douban_id = Column(String(50), nullable=True, index=True)

    # Media details
    year = Column(Integer, nullable=True, index=True)
    release_date = Column(DateTime, nullable=True)
    runtime = Column(Integer, nullable=True)  # Runtime in minutes

    # TV/Anime specific
    season_number = Column(Integer, nullable=True)
    episode_count = Column(Integer, nullable=True)

    # Ratings and popularity
    tmdb_rating = Column(Float, nullable=True)
    imdb_rating = Column(Float, nullable=True)
    douban_rating = Column(Float, nullable=True)
    popularity = Column(Float, nullable=True)

    # Content information
    overview = Column(Text, nullable=True)
    genres = Column(JSON, nullable=True)  # List of genre names
    cast = Column(JSON, nullable=True)  # List of cast members
    crew = Column(JSON, nullable=True)  # List of crew members

    # Images
    poster_url = Column(String(500), nullable=True)
    backdrop_url = Column(String(500), nullable=True)

    # Language and region
    original_language = Column(String(10), nullable=True)
    spoken_languages = Column(JSON, nullable=True)
    production_countries = Column(JSON, nullable=True)

    # Extra data
    extra_data = Column(JSON, nullable=True)  # Additional metadata from various sources

    # Relationships
    files = relationship("MediaFile", back_populates="media", cascade="all, delete-orphan")

    # ==================== 媒体仓储操作 ====================

    @staticmethod
    @db_query
    def get_by_tmdb_id(db: Session, tmdb_id: int) -> Optional['Media']:
        """通过TMDB ID获取媒体"""
        return db.query(Media).filter(Media.tmdb_id == tmdb_id).first()

    @staticmethod
    @db_query
    def get_by_imdb_id(db: Session, imdb_id: str) -> Optional['Media']:
        """通过IMDB ID获取媒体"""
        return db.query(Media).filter(Media.imdb_id == imdb_id).first()

    @staticmethod
    @db_query
    def get_by_douban_id(db: Session, douban_id: str) -> Optional['Media']:
        """通过豆瓣ID获取媒体"""
        return db.query(Media).filter(Media.douban_id == douban_id).first()

    @staticmethod
    @db_query
    def get_by_title_and_year(db: Session, title: str, year: Optional[int] = None) -> Optional['Media']:
        """通过标题和年份获取媒体"""
        query = db.query(Media).filter(Media.title == title)
        if year:
            query = query.filter(Media.year == year)
        return query.first()

    @staticmethod
    @db_query
    def search_by_title(db: Session, title: str, limit: int = 10) -> List['Media']:
        """通过标题搜索媒体"""
        return list(
            db.query(Media)
            .filter(
                or_(
                    Media.title.ilike(f"%{title}%"),
                    Media.original_title.ilike(f"%{title}%")
                )
            )
            .limit(limit)
            .all()
        )

    @staticmethod
    @db_query
    def get_by_type(db: Session, media_type: MediaType, limit: int = 100) -> List['Media']:
        """按类型获取媒体"""
        return list(
            db.query(Media)
            .filter(Media.type == media_type.value)
            .limit(limit)
            .all()
        )

    @staticmethod
    @db_query
    def get_by_status(db: Session, status: MediaStatus, limit: int = 100) -> List['Media']:
        """按状态获取媒体"""
        return list(
            db.query(Media)
            .filter(Media.status == status.value)
            .limit(limit)
            .all()
        )

    @staticmethod
    @db_query
    def get_wanted_media(db: Session, limit: int = 100) -> List['Media']:
        """获取想要的媒体"""
        return Media.get_by_status(db, MediaStatus.WANTED, limit)

    @staticmethod
    @db_query
    def get_downloaded_media(db: Session, limit: int = 100) -> List['Media']:
        """获取已下载的媒体"""
        return Media.get_by_status(db, MediaStatus.DOWNLOADED, limit)

    @staticmethod
    @db_query
    def get_by_year_range(db: Session, start_year: int, end_year: int) -> List['Media']:
        """按年份范围获取媒体"""
        return list(
            db.query(Media)
            .filter(
                and_(
                    Media.year >= start_year,
                    Media.year <= end_year
                )
            )
            .all()
        )

    @staticmethod
    @db_query
    def get_popular_media(db: Session, limit: int = 20) -> List['Media']:
        """获取热门媒体"""
        return list(
            db.query(Media)
            .filter(Media.popularity.isnot(None))
            .order_by(Media.popularity.desc())
            .limit(limit)
            .all()
        )

    @staticmethod
    @db_query
    def get_high_rated_media(db: Session, min_rating: float = 7.0, limit: int = 20) -> List['Media']:
        """获取高评分媒体"""
        return list(
            db.query(Media)
            .filter(
                or_(
                    Media.tmdb_rating >= min_rating,
                    Media.imdb_rating >= min_rating,
                    Media.douban_rating >= min_rating
                )
            )
            .limit(limit)
            .all()
        )

    @staticmethod
    @db_update
    def update_status(db: Session, media: 'Media', status: MediaStatus) -> 'Media':
        """更新媒体状态"""
        media.status = status.value
        db.add(media)
        return media

    @staticmethod
    @db_update
    def mark_as_downloaded(db: Session, media: 'Media') -> 'Media':
        """标记为已下载"""
        return Media.update_status(db, media, MediaStatus.DOWNLOADED)

    @staticmethod
    @db_update
    def mark_as_wanted(db: Session, media: 'Media') -> 'Media':
        """标记为想要"""
        return Media.update_status(db, media, MediaStatus.WANTED)

    @staticmethod
    @db_query
    def get_statistics(db: Session) -> Dict[str, Any]:
        """获取媒体统计信息"""
        total = db.query(Media).count()
        movies = db.query(Media).filter(Media.type == MediaType.MOVIE.value).count()
        tv_shows = db.query(Media).filter(Media.type == MediaType.TV.value).count()
        anime = db.query(Media).filter(Media.type == MediaType.ANIME.value).count()
        wanted = db.query(Media).filter(Media.status == MediaStatus.WANTED.value).count()
        downloaded = db.query(Media).filter(Media.status == MediaStatus.DOWNLOADED.value).count()

        return {
            "total": total,
            "by_type": {
                "movies": movies,
                "tv_shows": tv_shows,
                "anime": anime,
                "other": total - movies - tv_shows - anime
            },
            "by_status": {
                "wanted": wanted,
                "downloaded": downloaded,
                "other": total - wanted - downloaded
            }
        }


class MediaFile(BaseModel):
    """
    Individual media files
    集成媒体文件相关的所有数据库操作
    """
    __tablename__ = "media_files"

    media_id = Column(Integer, ForeignKey("media.id"), nullable=False)

    # File information
    filename = Column(String(500), nullable=False)
    original_filename = Column(String(500), nullable=True)
    file_path = Column(String(1000), nullable=False)
    file_size = Column(Integer, nullable=True)  # Size in bytes
    file_hash = Column(String(64), nullable=True, index=True)  # File hash for deduplication

    # Episode information (for TV shows)
    season_number = Column(Integer, nullable=True)
    episode_number = Column(Integer, nullable=True)
    episode_title = Column(String(500), nullable=True)

    # Quality information
    resolution = Column(String(20), nullable=True)  # 1080p, 4K, etc.
    codec = Column(String(50), nullable=True)  # H.264, H.265, etc.
    audio_codec = Column(String(50), nullable=True)  # AAC, DTS, etc.
    source = Column(String(50), nullable=True)  # BluRay, WEB-DL, etc.

    # Processing status
    processed = Column(Boolean, default=False)
    renamed = Column(Boolean, default=False)
    uploaded = Column(Boolean, default=False)

    # Upload information
    upload_path = Column(String(1000), nullable=True)
    cloud_url = Column(String(500), nullable=True)

    # File metadata
    file_metadata = Column(JSON, nullable=True)  # Additional file metadata
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    media = relationship("Media", back_populates="files")

    # ==================== 媒体文件仓储操作 ====================

    @staticmethod
    @db_query
    def get_by_media_id(db: Session, media_id: int) -> List['MediaFile']:
        """获取媒体的所有文件"""
        return list(db.query(MediaFile).filter(MediaFile.media_id == media_id).all())

    @staticmethod
    @db_query
    def get_by_file_path(db: Session, file_path: str) -> Optional['MediaFile']:
        """通过文件路径获取媒体文件"""
        return db.query(MediaFile).filter(MediaFile.file_path == file_path).first()

    @staticmethod
    @db_query
    def get_processed_files(db: Session) -> List['MediaFile']:
        """获取已处理的文件"""
        return list(db.query(MediaFile).filter(MediaFile.processed == True).all())

    @staticmethod
    @db_query
    def get_unprocessed_files(db: Session) -> List['MediaFile']:
        """获取未处理的文件"""
        return list(db.query(MediaFile).filter(MediaFile.processed == False).all())

    @staticmethod
    @db_update
    def mark_as_processed(db: Session, media_file: 'MediaFile') -> 'MediaFile':
        """标记文件为已处理"""
        media_file.processed = True
        media_file.processed_at = func.now()
        db.add(media_file)
        return media_file

    @staticmethod
    @db_query
    def get_by_resolution(db: Session, resolution: str) -> List['MediaFile']:
        """按分辨率获取文件"""
        return list(db.query(MediaFile).filter(MediaFile.resolution == resolution).all())

    @staticmethod
    @db_query
    def get_by_codec(db: Session, codec: str) -> List['MediaFile']:
        """按编码获取文件"""
        return list(db.query(MediaFile).filter(MediaFile.codec == codec).all())

    @staticmethod
    @db_query
    def get_by_hash(db: Session, file_hash: str) -> Optional['MediaFile']:
        """通过文件哈希获取文件"""
        return db.query(MediaFile).filter(MediaFile.file_hash == file_hash).first()

    @staticmethod
    @db_query
    def get_duplicates(db: Session) -> List['MediaFile']:
        """获取重复文件（相同哈希）"""
        return list(
            db.query(MediaFile)
            .filter(
                MediaFile.file_hash.in_(
                    db.query(MediaFile.file_hash)
                    .group_by(MediaFile.file_hash)
                    .having(func.count(MediaFile.file_hash) > 1)
                )
            )
            .all()
        )

    @staticmethod
    @db_query
    def get_file_statistics(db: Session) -> Dict[str, Any]:
        """获取文件统计信息"""
        total_files = db.query(MediaFile).count()
        processed_files = db.query(MediaFile).filter(MediaFile.processed == True).count()
        total_size = db.query(func.sum(MediaFile.file_size)).scalar() or 0

        return {
            "total_files": total_files,
            "processed_files": processed_files,
            "unprocessed_files": total_files - processed_files,
            "total_size_bytes": total_size,
            "total_size_gb": round(total_size / (1024**3), 2) if total_size else 0
        }
