"""
Subscription schemas
"""
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class SubscriptionType(str, Enum):
    """Subscription source types"""
    TMDB = "tmdb"
    DOUBAN = "douban"
    MIKAN = "mikan"
    MANUAL = "manual"


class SubscriptionStatus(str, Enum):
    """Subscription status"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class Subscription(BaseModel):
    """
    Subscription model for tracking media subscriptions
    """
    __tablename__ = "subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    type = Column(String(50), nullable=False, default=SubscriptionType.MANUAL)
    status = Column(String(50), nullable=False, default=SubscriptionStatus.ACTIVE)

    # External IDs
    tmdb_id = Column(Integer, nullable=True, index=True)
    douban_id = Column(String(50), nullable=True, index=True)
    mikan_id = Column(String(50), nullable=True, index=True)

    # Subscription details
    description = Column(Text, nullable=True)
    poster_url = Column(String(500), nullable=True)
    year = Column(Integer, nullable=True)
    season_number = Column(Integer, nullable=True)
    total_episodes = Column(Integer, nullable=True)

    # RSS/Feed URL for Mikan subscriptions
    rss_url = Column(String(500), nullable=True)

    # Search keywords and filters
    keywords = Column(JSON, nullable=True)  # List of keywords
    exclude_keywords = Column(JSON, nullable=True)  # List of exclude keywords
    quality_filter = Column(String(100), nullable=True)  # e.g., "1080p", "4K"

    # Settings
    auto_download = Column(Boolean, default=True)
    auto_rename = Column(Boolean, default=True)
    auto_upload = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_checked = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    rules = relationship("SubscriptionRule", back_populates="subscription", cascade="all, delete-orphan")
    downloads = relationship("Download", back_populates="subscription")


class SubscriptionRule(BaseModel):
    """
    Custom rules for subscription filtering
    """
    __tablename__ = "subscription_rules"

    id = Column(Integer, primary_key=True, index=True)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"), nullable=False)

    # Rule details
    name = Column(String(255), nullable=False)
    rule_type = Column(String(50), nullable=False)  # "regex", "keyword", "quality", "size"
    pattern = Column(Text, nullable=False)
    is_include = Column(Boolean, default=True)  # True for include, False for exclude
    priority = Column(Integer, default=0)  # Higher priority rules are applied first

    # Settings
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subscription = relationship("Subscription", back_populates="rules")
