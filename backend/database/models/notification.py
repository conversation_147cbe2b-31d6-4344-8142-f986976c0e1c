"""
Notification schemas
"""
from enum import Enum

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, JSON
from sqlalchemy.sql import func

from .base import BaseModel


class NotificationType(str, Enum):
    """Notification type"""
    TELEGRAM = "telegram"
    WEBHOOK = "webhook"
    WECHAT = "wechat"
    EMAIL = "email"


class NotificationStatus(str, Enum):
    """Notification status"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    RETRY = "retry"


class NotificationEvent(str, Enum):
    """Notification events"""
    DOWNLOAD_STARTED = "download_started"
    DOWNLOAD_COMPLETED = "download_completed"
    DOWNLOAD_FAILED = "download_failed"
    SUBSCRIPTION_ADDED = "subscription_added"
    MEDIA_PROCESSED = "media_processed"
    UPLOAD_COMPLETED = "upload_completed"
    SYSTEM_ERROR = "system_error"


class Notification(BaseModel):
    """
    Notification model for tracking sent notifications
    """
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)

    # Notification details
    type = Column(String(50), nullable=False)
    event = Column(String(100), nullable=False)
    status = Column(String(50), nullable=False, default=NotificationStatus.PENDING)

    # Recipients
    recipient = Column(String(255), nullable=False)  # Chat ID, webhook URL, email, etc.

    # Content
    title = Column(String(500), nullable=False)
    message = Column(Text, nullable=False)

    # Additional data
    data = Column(JSON, nullable=True)  # Additional data for the notification

    # Delivery information
    sent_at = Column(DateTime(timezone=True), nullable=True)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class NotificationTemplate(BaseModel):
    """
    Notification templates for different events
    """
    __tablename__ = "notification_templates"

    id = Column(Integer, primary_key=True, index=True)

    # Template details
    name = Column(String(255), nullable=False, unique=True, index=True)
    event = Column(String(100), nullable=False)
    type = Column(String(50), nullable=False)

    # Template content
    title_template = Column(String(500), nullable=False)
    message_template = Column(Text, nullable=False)

    # Settings
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
