"""
Download schemas
"""
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class DownloadStatus(str, Enum):
    """Download status"""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    SEEDING = "seeding"


class DownloadType(str, Enum):
    """Download type"""
    TORRENT = "torrent"
    MAGNET = "magnet"
    HTTP = "http"
    CLOUD = "cloud"


class Download(BaseModel):
    """
    Download model for tracking download tasks
    """
    __tablename__ = "downloads"

    id = Column(Integer, primary_key=True, index=True)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"), nullable=True)

    # Download details
    name = Column(String(500), nullable=False, index=True)
    original_name = Column(String(500), nullable=True)
    type = Column(String(50), nullable=False, default=DownloadType.TORRENT)
    status = Column(String(50), nullable=False, default=DownloadStatus.PENDING)

    # URLs and identifiers
    download_url = Column(Text, nullable=False)  # Magnet link, torrent URL, etc.
    hash = Column(String(64), nullable=True, index=True)  # Torrent hash

    # File information
    size = Column(Integer, nullable=True)  # Size in bytes
    downloaded = Column(Integer, default=0)  # Downloaded bytes
    progress = Column(Float, default=0.0)  # Progress percentage (0-100)

    # Paths
    save_path = Column(String(1000), nullable=True)
    final_path = Column(String(1000), nullable=True)  # After renaming

    # Download client info
    client_type = Column(String(50), nullable=True)  # qbittorrent, transmission, etc.
    client_id = Column(String(100), nullable=True)  # ID in the download client

    # Speed and ETA
    download_speed = Column(Integer, default=0)  # Bytes per second
    upload_speed = Column(Integer, default=0)  # Bytes per second
    eta = Column(Integer, nullable=True)  # Estimated time in seconds

    # Ratios and seeding
    ratio = Column(Float, default=0.0)
    seeds = Column(Integer, default=0)
    peers = Column(Integer, default=0)

    # Processing flags
    auto_rename = Column(Boolean, default=True)
    auto_upload = Column(Boolean, default=True)
    renamed = Column(Boolean, default=False)
    uploaded = Column(Boolean, default=False)

    # Error information
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)

    # Extra data
    extra_data = Column(JSON, nullable=True)  # Additional metadata

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    subscription = relationship("Subscription", back_populates="downloads")
    tasks = relationship("DownloadTask", back_populates="download", cascade="all, delete-orphan")


class DownloadTask(BaseModel):
    """
    Individual tasks within a download (for multi-file torrents)
    """
    __tablename__ = "download_tasks"

    id = Column(Integer, primary_key=True, index=True)
    download_id = Column(Integer, ForeignKey("downloads.id"), nullable=False)

    # Task details
    name = Column(String(500), nullable=False)
    file_path = Column(String(1000), nullable=False)
    size = Column(Integer, nullable=False)
    downloaded = Column(Integer, default=0)
    progress = Column(Float, default=0.0)
    priority = Column(Integer, default=1)  # Download priority

    # Processing status
    completed = Column(Boolean, default=False)
    renamed = Column(Boolean, default=False)
    uploaded = Column(Boolean, default=False)

    # Final paths after processing
    renamed_path = Column(String(1000), nullable=True)
    upload_path = Column(String(1000), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    download = relationship("Download", back_populates="tasks")
