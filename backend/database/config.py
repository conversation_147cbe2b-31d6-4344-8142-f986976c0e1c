"""
数据库配置模块
集中管理数据库连接配置和参数
"""

from typing import Dict, Any
from sqlalchemy.pool import StaticPool, QueuePool

from backend.utils.config import settings


class DatabaseConfig:
    """
    数据库配置类
    负责管理数据库连接参数、连接池配置等
    """

    def __init__(self):
        # 基础配置
        self.database_url = settings.database_url

        # 数据库类型检测
        self.is_sqlite = "sqlite" in self.database_url.lower()
        self.is_postgres = "postgresql" in self.database_url.lower()
        self.is_mysql = "mysql" in self.database_url.lower()

        # 调试模式
        self.debug = settings.debug

    def get_engine_config(self) -> Dict[str, Any]:
        """获取引擎配置"""
        base_config = {
            "echo": self.debug and settings.get("database.echo", False),
            "echo_pool": self.debug and settings.get("database.echo_pool", False),
            "future": True,  # 使用SQLAlchemy 2.0风格
        }

        # 添加连接池配置
        base_config.update(self._get_pool_config())

        # 添加隔离级别（SQLite不支持）
        if not self.is_sqlite:
            base_config["isolation_level"] = "READ_COMMITTED"

        return base_config

    def _get_pool_config(self) -> Dict[str, Any]:
        """获取连接池配置"""
        if self.is_sqlite:
            return {
                "poolclass": StaticPool,
                "pool_pre_ping": True,
                "connect_args": {
                    "check_same_thread": False,
                    "timeout": 30
                }
            }
        else:
            return {
                "poolclass": QueuePool,
                "pool_size": settings.get("database.pool_size", 10),
                "max_overflow": settings.get("database.max_overflow", 20),
                "pool_pre_ping": True,
                "pool_recycle": settings.get("database.pool_recycle", 3600),
                "pool_timeout": settings.get("database.pool_timeout", 30),
                "connect_args": self._get_connect_args()
            }

    def _get_connect_args(self) -> Dict[str, Any]:
        """获取连接参数"""
        if self.is_postgres:
            return {
                "connect_timeout": 10,
                "server_settings": {
                    "application_name": "MediPaka",
                }
            }
        elif self.is_mysql:
            return {
                "connect_timeout": 10,
                "charset": "utf8mb4"
            }
        return {}

    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息摘要"""
        return {
            "type": self._get_db_type(),
            "url_masked": self._mask_url(self.database_url),
            "is_sqlite": self.is_sqlite,
            "is_postgres": self.is_postgres,
            "is_mysql": self.is_mysql,
            "debug": self.debug
        }

    def _get_db_type(self) -> str:
        """获取数据库类型名称"""
        if self.is_sqlite:
            return "SQLite"
        elif self.is_postgres:
            return "PostgreSQL"
        elif self.is_mysql:
            return "MySQL"
        return "Unknown"

    def _mask_url(self, url: str) -> str:
        """屏蔽URL中的敏感信息"""
        if "@" in url:
            return url.split("@")[-1]
        return url


# 全局配置实例
db_config = DatabaseConfig()
