"""
数据库初始化模块
负责数据库表创建、默认数据初始化等
"""

from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from .connection import db_connection
from .session import db_session
from .models.user import User
from .models.base import Base


class DatabaseInitializer:
    """
    数据库初始化器
    负责数据库的创建、初始化和管理
    """

    def __init__(self):
        self.connection = db_connection

    def create_tables(self) -> bool:
        """
        创建数据库表（如果不存在）
        """
        try:
            # 初始化引擎
            self.connection.initialize_engine()

            # 创建所有表
            Base.metadata.create_all(bind=self.connection.engine, checkfirst=True)
            return True
        except SQLAlchemyError as e:
            logger.error(f"创建数据库表失败: {e}")
            return False

    def drop_tables(self) -> bool:
        """
        删除所有数据库表
        """
        try:
            logger.warning("开始删除所有数据库表...")
            Base.metadata.drop_all(bind=self.connection.engine)
            logger.warning("所有数据库表已删除")
            return True
        except SQLAlchemyError as e:
            logger.error(f"删除数据库表失败: {e}")
            return False

    def create_default_admin(self) -> bool:
        """创建默认管理员用户"""
        admin_username = "admin"
        admin_password = "admin123"
        
        try:
            with db_session() as db:
                # 检查是否已存在管理员
                existing_user = User.get_by_username(db, admin_username)
                if existing_user:
                    return True

                # 创建默认管理员
                admin_user = User.create_user(
                    db=db,
                    username=admin_username,
                    password=admin_password,
                    is_active=True
                )

                logger.info(f"创建默认管理员用户成功: {admin_username} / {admin_password}")
                return True
                
        except Exception as e:
            logger.error(f"创建默认管理员用户失败: {e}")
            return False



    def initialize_database(self) -> bool:
        """
        完整的数据库初始化
        包括创建表和默认数据
        """
        try:
            # 检查连接
            if not self.connection.check_connection():
                logger.error("数据库连接失败，无法初始化")
                return False
            
            # 创建表
            if not self.create_tables():
                logger.error("创建数据库表失败")
                return False
            
            # 创建默认管理员
            if not self.create_default_admin():
                logger.error("创建默认管理员失败")
                return False

            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False



    def reset_database(self) -> bool:
        """
        重置数据库（删除所有表后重新创建）
        """
        try:
            logger.warning("开始重置数据库...")
            
            # 删除所有表
            if not self.drop_tables():
                logger.error("删除数据库表失败")
                return False
            
            # 重新初始化
            if not self.initialize_database():
                logger.error("重新初始化数据库失败")
                return False
            
            logger.warning("数据库重置完成")
            return True
            
        except Exception as e:
            logger.error(f"数据库重置失败: {e}")
            return False

    def get_database_status(self) -> dict:
        """获取数据库状态信息"""
        try:
            connection_info = self.connection.get_connection_info()
            
            # 检查表是否存在
            tables_exist = False
            try:
                with db_session() as db:
                    admin_count = User.count(db)
                    tables_exist = True
                    has_admin = admin_count > 0
            except:
                has_admin = False
            
            return {
                "connection_info": connection_info,
                "tables_exist": tables_exist,
                "has_admin_user": has_admin,
                "initialized": tables_exist and has_admin
            }
        except Exception as e:
            logger.error(f"获取数据库状态失败: {e}")
            return {"error": str(e)}


# 全局初始化器实例
db_initializer = DatabaseInitializer()


# 便捷函数
def init_database() -> bool:
    """初始化数据库"""
    return db_initializer.initialize_database()


def reset_database() -> bool:
    """重置数据库"""
    return db_initializer.reset_database()


def check_database_status() -> dict:
    """检查数据库状态"""
    return db_initializer.get_database_status()
