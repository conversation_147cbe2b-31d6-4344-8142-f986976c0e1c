#!/usr/bin/env python3
"""
MediPaka 启动脚本 - 唯一的应用启动入口点

该脚本负责：
1. 解析命令行参数
2. 设置运行环境（开发/生产）
3. 配置服务器参数
4. 启动 FastAPI 应用

支持开发环境和生产环境的启动配置，提供丰富的命令行选项

注意：请不要直接运行 main.py，统一使用此启动脚本
"""

import argparse
import os
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import uvicorn
from backend.utils.config import init_config, settings


def setup_environment(env: str) -> None:
    """设置环境变量"""
    os.environ["ENV"] = env


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="MediPaka 媒体自动化系统后端服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                       # 生产环境，默认配置
  %(prog)s --env dev --reload                    # 开发环境，启用热重载
  %(prog)s --env prod                            # 生产环境，默认配置
  %(prog)s --env dev --host 0.0.0.0 --port 8080 # 开发环境，自定义主机和端口
        """
    )

    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default="dev",
        help="运行环境 (默认: dev)"
    )

    parser.add_argument(
        "--host",
        default=None,
        help="服务器主机地址 (默认: 从配置读取)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口 (默认: 从配置读取)"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用热重载 (仅开发环境)"
    )

    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="禁用热重载"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=None,
        help="日志级别 (默认: 从配置读取)"
    )

    parser.add_argument(
        "--access-log",
        action="store_true",
        help="启用访问日志"
    )

    parser.add_argument(
        "--no-access-log",
        action="store_true",
        help="禁用访问日志"
    )

    return parser.parse_args()


def get_server_config(args, settings):
    """获取服务器配置"""
    # 基本配置
    server_config = {
        "host": args.host or settings.server_host,
        "port": args.port or settings.server_port,
        "log_level": (args.log_level or settings.log_level).lower(),
        "app": "backend.main:app"
    }

    # 环境特定配置
    if settings.is_development():
        reload_enabled = not args.no_reload and (args.reload or True)

        # 获取正确的backend目录路径
        backend_dir = Path(__file__).parent  # backend目录的绝对路径

        server_config.update({
            "reload": reload_enabled,
            "access_log": False,  # 禁用uvicorn访问日志，使用自定义日志
            "reload_dirs": [str(backend_dir)] if reload_enabled else None
        })
    elif settings.is_production():
        import platform
        # Windows兼容性处理
        is_windows = platform.system() == "Windows"
        loop_type = "asyncio" if is_windows else "uvloop"
        http_type = "h11" if is_windows else "httptools"  # Windows使用h11

        server_config.update({
            "reload": False,
            "access_log": False,  # 禁用uvicorn访问日志，使用自定义日志
            "loop": loop_type,  # 生产环境性能优化（Windows兼容）
            "http": http_type  # Windows兼容的HTTP实现
        })

    return server_config


def main():
    """主函数 - MediPaka 应用启动入口点"""
    try:
        # 解析参数
        args = parse_args()

        # 设置环境
        setup_environment(args.env)

        # 初始化配置
        init_config()

        # 获取服务器配置
        server_config = get_server_config(args, settings)

        # 启动服务器
        uvicorn.run(**server_config)

    except KeyboardInterrupt:
        print("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def signal_handler(signum, frame):
    """信号处理器"""
    from loguru import logger as logger
    logger.info(f"收到信号 {signum}，正在优雅关闭...")
    sys.exit(0)


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    main()
