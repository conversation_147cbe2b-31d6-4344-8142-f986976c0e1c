[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "medipaka"
version = "1.0.0"
description = "媒体自动化系统 - 支持订阅管理、资源搜索、自动下载和媒体库整理"
readme = "README.md"
license = { file = "LICENSE" }
authors = [
    { name = "MediPaka Team", email = "<EMAIL>" }
]
maintainers = [
    { name = "MediPaka Team", email = "<EMAIL>" }
]
keywords = [
    "media",
    "automation",
    "download",
    "torrent",
    "subscription",
    "tmdb",
    "telegram"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Multimedia :: Video",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.9"
dependencies = [
    # ===== Web框架 =====
    "fastapi>=0.116.0,<0.117.0",
    "uvicorn[standard]>=0.22.0,<0.23.0",
    "starlette>=0.47.0,<0.48.0",

    # ===== 数据验证 =====
    "pydantic>=2.11.0,<2.12.0",
    "pydantic-settings>=2.10.0,<2.11.0",

    # ===== 数据库 =====
    "sqlalchemy>=2.0.40,<2.1.0",
    "alembic>=1.11.0,<1.12.0",
    "aiosqlite>=0.21.0,<0.22.0",

    # ===== 认证和安全 =====
    "python-jose[cryptography]>=3.3.0,<3.4.0",

    # ===== HTTP客户端 =====
    "httpx>=0.28.0,<0.29.0",

    # ===== 文件处理 =====
    "python-multipart>=0.0.6,<0.1.0",

    # ===== 配置管理 =====
    "python-dotenv>=1.0.0,<1.1.0",

    # ===== 日志 =====
    "loguru>=0.7.0,<0.8.0",
]

[project.optional-dependencies]
# ===== 开发环境依赖 =====
dev = [
    # 测试框架
    "pytest>=7.4.0,<8.0.0",
    "pytest-asyncio>=0.21.0,<0.22.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "pytest-html>=3.2.0,<4.0.0",

    # 代码质量工具
    "black>=23.0.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "flake8>=6.0.0,<7.0.0",
    "mypy>=1.7.0,<2.0.0",
    "ruff>=0.1.0,<0.2.0",

    # 代码检查和格式化
    "pre-commit>=3.5.0,<4.0.0",
    "coverage>=7.3.0,<8.0.0",

    # 类型检查支持
    "types-requests>=2.31.0",
    "types-PyYAML>=6.0.0",

    # 开发工具
    "watchdog>=3.0.0,<4.0.0",
    "rich>=13.0.0,<14.0.0",

    # 调试工具
    "ipython>=8.0.0,<9.0.0",
    "ipdb>=0.13.0,<1.0.0",
]

# ===== 生产环境优化 =====
prod = [
    "uvloop>=0.19.0,<0.20.0; sys_platform != 'win32'",
    "httptools>=0.6.0,<0.7.0; sys_platform != 'win32'",
    "gunicorn>=23.0.0,<24.0.0; sys_platform != 'win32'",
]

# ===== 媒体功能依赖 =====
media = [
    # 图像处理
    "Pillow>=10.0.0,<11.0.0",

    # 网页抓取和解析
    "beautifulsoup4>=4.12.0,<5.0.0",
    "lxml>=4.9.0,<5.0.0",
    "requests>=2.31.0,<3.0.0",

    # RSS和Feed处理
    "feedparser>=6.0.10,<7.0.0",

    # 外部API客户端
    "tmdbv3api>=1.9.0,<2.0.0",
]

# ===== 下载客户端支持 =====
downloaders = [
    # qBittorrent支持
    "qbittorrent-api>=2023.11.0,<2024.0.0",

    # Transmission支持
    "transmission-rpc>=7.0.0,<8.0.0",
]

# ===== 通知服务 =====
notifications = [
    # Telegram通知
    "python-telegram-bot>=20.7,<21.0",
]

# ===== 任务调度 =====
scheduler = [
    "APScheduler>=3.10.0,<4.0.0",
]

# ===== 配置文件支持 =====
config = [
    "PyYAML>=6.0.1,<7.0.0",
]

# ===== 缓存和性能 =====
cache = [
    "cachetools>=5.3.0,<6.0.0",
]

# ===== 数据处理 =====
data = [
    "pandas>=2.0.0,<3.0.0",
    "numpy>=1.24.0,<2.0.0",
]

# ===== 文档生成 =====
docs = [
    "mkdocs>=1.5.0,<2.0.0",
    "mkdocs-material>=9.4.0,<10.0.0",
    "mkdocstrings[python]>=0.24.0,<1.0.0",
]

# ===== 完整功能集合 =====
full = [
    "medipaka[media,downloaders,notifications,scheduler,config,cache]",
]

# ===== 所有依赖 (开发 + 完整功能) =====
all = [
    "medipaka[dev,full]",
]

[project.scripts]
medipaka = "backend.main:main"
medipaka-dev = "backend.run:main"

[tool.hatch.build.targets.wheel]
packages = ["backend"]

[tool.black]
line-length = 88
target-version = ["py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["backend"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "uvicorn"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
    "*.egg-info",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "qbittorrentapi.*",
    "transmission_rpc.*",
    "feedparser.*",
    "bs4.*",
    "tmdbv3api.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["backend"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
target-version = "py39"
line-length = 88
select = [
    "E", # pycodestyle errors
    "W", # pycodestyle warnings
    "F", # pyflakes
    "I", # isort
    "B", # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]
