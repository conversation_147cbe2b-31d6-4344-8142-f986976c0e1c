# MediPaka - 媒体自动化系统

MediPaka 是一个全功能的媒体自动化系统，支持订阅管理、资源搜索、自动下载、文件重命名和云存储上传。

## ✨ 功能特性

### 1. 📡 订阅模块（TMDB / 豆瓣 / Mikan）

#### 功能：
* 解析订阅源（如 Mikan RSS）
* 定时抓取更新数据
* 支持用户订阅的番剧/影视条目（关键词 + 正则过滤）
* 支持通过Emby查询电视剧的遗漏集

#### 技术点：
* 使用 `feedparser` 或 `aiohttp` 获取 RSS 数据
* TMDB/豆瓣 API 拉取条目信息
  

### 2. 🔍 资源搜索模块（CloudSaver / Nullbr / TG）

#### 功能：

* 自定义站源（类似 mediahelp/mediaautomator 的站点匹配规则）
  
* Telegram频道搜索接口（Bot API + TG非官方库如Telethon）
  
* Nullbr、Rarbg等资源站点接口
  
* 内置正则规则用于提取关键字段
  

#### 技术点：

* 使用 `requests`/`aiohttp` 对资源站点做爬虫封装
  
* `Telethon` 实现 Telegram 频道历史消息搜索
  
* 支持 YAML 规则匹配资源标题
  

### 3. 💾 网盘转存 & 实时监控模块

#### 功能：

* 下载完成自动转存至 Alist/115/OneDrive
  
* 定时任务或监听文件夹变化实现实时上传
  
* 支持使用 hash 比对去重、校验文件名
  

#### 技术点：

* Alist 提供 REST API，直接 `requests` 上传
  
* 使用 `watchdog` 实现文件夹变化监听
  
* 使用 `APScheduler` 定时轮询目标目录
  

### 4. 🧲 下载器控制模块（QB/TR/Aria2/Alist/各种网盘）

#### 功能：

* 管理种子上传、下载状态查询
  
* 多下载器兼容，支持插件扩展
  
* 下载完毕自动回传事件至通知/转存模块
  

### 5. ✍️ TMDB资源重命名模块

#### 功能：

* 根据 TMDB 元数据重命名剧集/电影（支持多语言）
  
* 支持识别片源版本、清晰度、季/集等
  
* 支持重命名模版，如 `剧名 - S01E02 - 标题 [字幕组].mkv`
  

#### 技术点：

* 使用 `tmdbsimple` 或 `tmdbv3api` 获取元信息
  
* 提供重命名模版配置与占位符替换
  

### 6. 🔔 通知系统

#### 功能：

* 支持 Webhook 推送
  
* 支持 Telegram / 微信 Bot 消息推送
  
* 支持 Emby 刷新元数据接口调用
  

#### 技术点：

* Telegram Bot：`python-telegram-bot`
  
* 企业微信：Webhook / 应用推送
  
* Emby：`/Library/Refresh` 接口（需 Token）
  

### ✅ 后端技术栈

* **后端框架**：FastAPI（支持异步、插件化）
  
* **任务调度**：APScheduler + Celery（可选）
  
* **数据库**：SQLite
  
* **消息推送**：Webhook / Telegram / 企业微信
  
* **下载模块**：Qb 、Tr、aria2
  
* **存储接入**：模块化网盘插件（Alist、115、天翼云、百度盘等）
  

✅ 前端技术栈（Vue3 + Vite + pnpm + ElmentPlus）

### 页面结构建议

| 页面名称 | 说明  |
| --- | --- |
| 首页 Dashboard | 展示最新订阅资源 / 下载队列状态 |
| 订阅管理页 | 添加 TMDB/Mikan/豆瓣条目 |
| 下载器管理页 | 插件列表、启用（Alist、115、Qb等） |
| 下载队列页 | 下载器任务状态 / 上传状态 |
| 设置页 | 系统配置 / 账号配置 /Token 管理 / Emby同步/通知管理 |

## 🚀 快速开始

### 环境要求
- Python 3.11+
- qBittorrent (可选，用于种子下载)
- Alist (可选，用于云存储)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/MediPaka.git
cd MediPaka
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的配置
```

4. **初始化系统**
```bash
python start.py
```

5. **启动服务**
```bash
python main.py
```

### Docker 部署

```bash
# 使用 Docker Compose 一键部署
docker-compose up -d
```

## 📖 配置说明

### 必需配置
- `TMDB_API_KEY`: TMDB API密钥 (必需)

### 可选配置
- `TELEGRAM_BOT_TOKEN`: Telegram机器人Token
- `QB_PASSWORD`: qBittorrent密码
- `ALIST_BASE_URL`: Alist服务地址

## 🔧 API文档

启动服务后访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 📋 工作流程示例

1. 用户订阅某个豆瓣榜单的媒体或 Mikan 番剧 → 加入订阅队列
2. 系统每小时解析订阅源并搜索资源 → 发现匹配资源
   * 资源可以通过 Nullbr 或 TG 频道获取 magnet、网盘链接
3. 匹配到资源下载器接手下载任务
4. 下载完成 → 重命名 → 上传至网盘（如 Alist）
5. 推送通知至 TG机器人 、WebHook → 触发 Emby 刷新媒体库

---

## 🎨 前端界面特性

### 💻 桌面端
- **现代化设计**: 基于Vue3 + TypeScript + Element Plus
- **响应式布局**: 适配各种屏幕尺寸
- **主题切换**: 支持明暗主题和自动跟随系统
- **实时更新**: WebSocket实时状态监控

### 📱 移动端
- **原生体验**: 完整的移动端适配
- **触摸优化**: 手势支持、触摸反馈
- **底部导航**: 符合移动端使用习惯
- **安全区域**: 支持iPhone刘海屏等异形屏

### 🔧 技术特性
- **类型安全**: 完整的TypeScript支持
- **组件化**: 可复用的Vue3组件
- **状态管理**: Pinia响应式状态管理
- **路由管理**: Vue Router 4动态路由

## 🤝 贡献指南

### 🐛 报告问题
- 使用 [GitHub Issues](https://github.com/medipaka/medipaka/issues) 报告bug
- 提供详细的复现步骤和环境信息

### 💡 功能建议
- 在 [GitHub Discussions](https://github.com/medipaka/medipaka/discussions) 讨论新功能
- 详细描述功能需求和使用场景

### 🔧 代码贡献
1. Fork 项目到你的GitHub账户
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'feat: add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

### 📝 提交规范
我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 其他杂项

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢以下开源项目：
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3组件库
- [SQLAlchemy](https://www.sqlalchemy.org/) - Python SQL工具包
- [Pinia](https://pinia.vuejs.org/) - Vue状态管理库

---

⭐ 如果这个项目对你有帮助，请给我们一个Star！