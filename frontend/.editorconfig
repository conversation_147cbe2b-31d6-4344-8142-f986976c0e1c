# EditorConfig 配置文件
# 
# EditorConfig 帮助开发者在不同的编辑器和 IDE 之间
# 定义和维护一致的编码样式
# 
# 更多信息请访问: https://editorconfig.org

# 表示这是根配置文件，停止向上查找
root = true

# 所有文件的通用配置
[*]
# 字符集
charset = utf-8
# 换行符类型 (auto: 自动检测，与 Prettier 保持一致)
end_of_line = lf
# 文件末尾插入新行
insert_final_newline = true
# 去除行尾空白字符
trim_trailing_whitespace = true
# 缩进样式 (space: 空格, tab: 制表符)
indent_style = space
# 缩进大小
indent_size = 2

# JavaScript 和 TypeScript 文件
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# Vue 单文件组件
[*.vue]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON 文件
[*.json]
indent_style = space
indent_size = 2
# JSON 文件通常不需要末尾新行
insert_final_newline = false

# YAML 文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# CSS, SCSS, LESS 文件
[*.{css,scss,sass,less}]
indent_style = space
indent_size = 2
max_line_length = 120

# HTML 文件
[*.{html,htm}]
indent_style = space
indent_size = 2
max_line_length = 120

# XML 文件
[*.{xml,svg}]
indent_style = space
indent_size = 2

# Markdown 文件
[*.{md,mdx}]
indent_style = space
indent_size = 2
# Markdown 文件保留行尾空白（用于换行）
trim_trailing_whitespace = false
max_line_length = 80

# 配置文件
[*.config.{js,ts}]
indent_style = space
indent_size = 2

# 包管理器文件
[{package.json,package-lock.json,yarn.lock,pnpm-lock.yaml}]
indent_style = space
indent_size = 2

# 环境变量文件
[.env*]
indent_style = space
indent_size = 2
# 环境变量文件可能不需要末尾新行
insert_final_newline = false

# Makefile (必须使用制表符)
[{Makefile,makefile,*.mk}]
indent_style = tab
indent_size = 4

# Go 文件 (Go 官方推荐使用制表符)
[*.go]
indent_style = tab
indent_size = 4

# Python 文件
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# Shell 脚本
[*.{sh,bash,zsh}]
indent_style = space
indent_size = 2

# PowerShell 脚本
[*.{ps1,psm1,psd1}]
indent_style = space
indent_size = 4

# Batch 文件
[*.{bat,cmd}]
indent_style = space
indent_size = 2
end_of_line = crlf

# Docker 文件
[{Dockerfile,Dockerfile.*,*.dockerfile}]
indent_style = space
indent_size = 2

# 忽略某些文件类型的配置
[*.{min.js,min.css}]
# 压缩文件不进行格式化
insert_final_newline = false
trim_trailing_whitespace = false

# 二进制文件
[*.{png,jpg,jpeg,gif,ico,svg,woff,woff2,ttf,eot}]
# 二进制文件不进行任何处理
insert_final_newline = false
trim_trailing_whitespace = false

# 大型数据文件
[*.{log,csv,tsv}]
# 日志和数据文件保持原样
trim_trailing_whitespace = false
