<template>
  <div class="app-container" :class="{ 'sidebar-open': sidebarOpen }">
    <!-- 顶部导航栏 -->
    <TopNavigation
      @toggle-sidebar="toggleSidebar"
      @toggle-theme="toggleTheme"
      @search="handleSearch"
    />

    <!-- 侧边栏导航 -->
    <SidebarNavigation
      :is-open="sidebarOpen"
      @close="closeSidebar"
    />

    <!-- 主内容区域 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="page-fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 移动端底部导航 -->
    <BottomNavigation class="mobile-only" />

    <!-- 侧边栏遮罩层 (移动端) -->
    <div
      v-if="sidebarOpen"
      class="sidebar-overlay mobile-only"
      @click="closeSidebar"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLayoutStore } from '@/stores'
import TopNavigation from './components/TopNavigation.vue'
import SidebarNavigation from './components/SidebarNavigation.vue'
import BottomNavigation from './components/BottomNavigation.vue'

const router = useRouter()
const layoutStore = useLayoutStore()

// 从store获取状态
const { sidebarOpen } = layoutStore

// 切换侧边栏
const toggleSidebar = () => {
  layoutStore.toggleSidebar()
}

// 关闭侧边栏
const closeSidebar = () => {
  layoutStore.closeSidebar()
}

// 主题切换
const toggleTheme = () => {
  layoutStore.toggleTheme()
}

// 搜索处理
const handleSearch = (query: string) => {
  if (query.trim()) {
    router.push({ name: 'Search', query: { q: query } })
  }
}

let cleanupLayout: (() => void) | undefined

onMounted(() => {
  cleanupLayout = layoutStore.initLayout()
})

onUnmounted(() => {
  if (cleanupLayout) {
    cleanupLayout()
  }
})
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
}

.main-content {
  flex: 1;
  margin-left: 0;
  margin-top: var(--topnav-height);
  padding: 1.5rem;
  transition: var(--transition);

  @media (min-width: 769px) {
    margin-left: var(--sidebar-width);
  }

  @media (max-width: 768px) {
    margin-bottom: var(--bottomnav-height);
    padding: 1rem;
  }
}

// 侧边栏遮罩层
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  z-index: var(--z-modal-backdrop);
  backdrop-filter: blur(4px);
}

// 页面切换动画
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 响应式显示控制
.mobile-only {
  @media (min-width: 769px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 768px) {
    display: none !important;
  }
}
</style>
