<template>
  <nav class="bottom-nav glass-effect">
    <button
      v-for="item in navItems"
      :key="item.name"
      class="nav-item"
      :class="{ 'active': isActiveRoute(item.name) }"
      @click="navigateTo(item.name)"
    >
      <div class="nav-icon">
        <el-icon class="bottom-nav-icon"><component :is="item.icon" /></el-icon>
        <div v-if="isActiveRoute(item.name)" class="active-indicator"></div>
      </div>
      <span class="nav-label">{{ item.label }}</span>
    </button>
  </nav>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import {
  TrendCharts,
  Rss,
  Download,
  Search,
  Setting
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 导航项配置
const navItems = [
  {
    name: 'Dashboard',
    label: '首页',
    icon: TrendCharts,
    path: '/dashboard'
  },
  {
    name: 'Subscriptions',
    label: '订阅',
    icon: Rss,
    path: '/subscriptions'
  },
  {
    name: 'Downloads',
    label: '下载',
    icon: Download,
    path: '/downloads'
  },
  {
    name: 'Search',
    label: '搜索',
    icon: Search,
    path: '/search'
  },
  {
    name: 'Settings',
    label: '设置',
    icon: Setting,
    path: '/settings'
  }
]

// 检查当前路由是否激活
const isActiveRoute = (routeName: string) => {
  return route.name === routeName
}

// 导航到指定路由
const navigateTo = (routeName: string) => {
  const item = navItems.find(item => item.name === routeName)
  if (item) {
    router.push(item.path)
  }
}
</script>

<style scoped lang="scss">
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottomnav-height);
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  padding: 0.5rem 0;

  // 安全区域适配
  padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  border-radius: var(--border-radius);
  min-width: 60px;
  position: relative;

  &:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
    transform: translateY(-2px);
  }

  &.active {
    color: var(--primary-color);

    .nav-icon {
      transform: translateY(-2px);
    }

    .nav-label {
      font-weight: 600;
    }
  }

  // 点击反馈动画
  &:active {
    transform: scale(0.95);
  }
}

.nav-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  transition: var(--transition);

  i {
    font-size: 1.2rem;
    transition: var(--transition);
  }
}

.active-indicator {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: bounce 0.6s ease;
}

.nav-label {
  font-size: 0.75rem;
  font-weight: 500;
  transition: var(--transition);
  text-align: center;
  line-height: 1;
}

@keyframes bounce {
  0% {
    transform: translateX(-50%) translateY(10px);
    opacity: 0;
  }
  50% {
    transform: translateX(-50%) translateY(-2px);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

// 波纹点击效果
.nav-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  opacity: 0;
  z-index: -1;
}

.nav-item:active::before {
  width: 100px;
  height: 100px;
  opacity: 0.3;
}

// 响应式适配
@media (max-width: 480px) {
  .nav-item {
    min-width: 50px;
    padding: 0.4rem 0.5rem;
  }

  .nav-icon {
    width: 1.75rem;
    height: 1.75rem;

    i {
      font-size: 1.1rem;
    }
  }

  .nav-label {
    font-size: 0.7rem;
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .bottom-nav {
    height: 50px;
    padding: 0.25rem 0;
  }

  .nav-item {
    gap: 0.125rem;
    padding: 0.25rem 0.5rem;
  }

  .nav-icon {
    width: 1.5rem;
    height: 1.5rem;

    i {
      font-size: 1rem;
    }
  }

  .nav-label {
    font-size: 0.65rem;
  }
}
</style>
