<template>
  <nav class="sidebar glass-effect" :class="{ 'open': isOpen }">
    <div class="sidebar-content">
      <!-- 导航菜单 -->
      <ul class="nav-menu">
        <li
          v-for="item in menuItems"
          :key="item.name"
          class="nav-item"
          :class="{ 'active': isActiveRoute(item.name) }"
          @click="navigateTo(item.name)"
        >
          <div class="nav-link">
            <el-icon class="sidebar-nav-icon"><component :is="item.icon" /></el-icon>
            <span class="nav-text">{{ item.label }}</span>
            <div class="nav-indicator"></div>
          </div>
        </li>
      </ul>

      <!-- 底部信息 -->
      <div class="sidebar-footer">
        <div class="version-info">
          <span class="version-label">版本</span>
          <span class="version-number">v1.0.0</span>
        </div>
        <div class="status-indicator">
          <div class="status-dot online"></div>
          <span>系统正常</span>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  TrendCharts,
  Rss,
  Download,
  Search,
  Setting
} from '@element-plus/icons-vue'

// Props
interface Props {
  isOpen: boolean
}

defineProps<Props>()

// 事件
const emit = defineEmits<{
  'close': []
}>()

const router = useRouter()
const route = useRoute()

// 菜单项配置
const menuItems = [
  {
    name: 'Dashboard',
    label: '仪表盘',
    icon: TrendCharts,
    path: '/dashboard'
  },
  {
    name: 'Subscriptions',
    label: '订阅管理',
    icon: Rss,
    path: '/subscriptions'
  },
  {
    name: 'Downloads',
    label: '下载队列',
    icon: Download,
    path: '/downloads'
  },
  {
    name: 'Search',
    label: '资源搜索',
    icon: Search,
    path: '/search'
  },
  {
    name: 'Settings',
    label: '系统设置',
    icon: Setting,
    path: '/settings'
  }
]

// 检查当前路由是否激活
const isActiveRoute = (routeName: string) => {
  return route.name === routeName
}

// 导航到指定路由
const navigateTo = (routeName: string) => {
  const item = menuItems.find(item => item.name === routeName)
  if (item) {
    router.push(item.path)
    // 移动端导航后关闭侧边栏
    if (window.innerWidth <= 768) {
      emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
.sidebar {
  position: fixed;
  top: var(--topnav-height);
  left: 0;
  width: var(--sidebar-width);
  height: calc(100vh - var(--topnav-height));
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  transform: translateX(-100%);
  transition: var(--transition);
  overflow-y: auto;

  &.open {
    transform: translateX(0);
  }

  @media (min-width: 769px) {
    transform: translateX(0);
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.5rem 0;
}

.nav-menu {
  flex: 1;
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  position: relative;
  margin: 0.25rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;

  &:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
  }

  &.active {
    background: linear-gradient(135deg, var(--primary-color), var(--purple-accent));
    color: white;

    .nav-indicator {
      opacity: 1;
      transform: scaleY(1);
    }

    .nav-link i {
      color: white;
    }
  }
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  text-decoration: none;
  color: var(--text-primary);
  position: relative;

  i {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-right: 1rem;
    transition: var(--transition);
    width: 1.5rem;
    text-align: center;
  }
}

.nav-text {
  font-weight: 500;
  font-size: 0.95rem;
}

.nav-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%) scaleY(0);
  width: 4px;
  height: 60%;
  background: var(--accent-color);
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: var(--transition);
}

.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.version-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
  color: var(--text-muted);
}

.version-number {
  background: var(--bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-family: monospace;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.online {
    background: var(--success-color);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
}

// 滚动条样式
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;

  &:hover {
    background: var(--text-muted);
  }
}
</style>
