<template>
  <header class="top-nav glass-effect">
    <div class="nav-left">
      <!-- 菜单切换按钮 (移动端) -->
      <el-button
        class="menu-toggle mobile-only top-nav-btn"
        type="default"
        text
        @click="$emit('toggle-sidebar')"
      >
        <el-icon><Menu /></el-icon>
      </el-button>

      <!-- Logo -->
      <div class="logo" @click="$router.push('/')">
        <el-icon class="logo-icon"><VideoPlay /></el-icon>
        <span class="rainbow-text">MediPaka</span>
      </div>
    </div>

    <!-- 搜索框 (桌面端) -->
    <div class="nav-center desktop-only">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索媒体资源..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 右侧操作区 -->
    <div class="nav-right">
      <!-- 搜索按钮 (移动端) -->
      <el-button
        class="search-btn mobile-only top-nav-btn"
        type="default"
        text
        @click="showMobileSearch = !showMobileSearch"
      >
        <el-icon><Search /></el-icon>
      </el-button>

      <!-- 主题切换 -->
      <el-button
        class="theme-btn glow-button top-nav-btn"
        type="default"
        text
        @click="$emit('toggle-theme')"
        v-el-tooltip="isDarkTheme ? '切换到明亮主题' : '切换到深色主题'"
      >
        <el-icon>
          <component :is="isDarkTheme ? Sunny : Moon" />
        </el-icon>
      </el-button>

      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserMenuCommand">
        <el-button
          class="user-btn top-nav-btn"
          type="default"
          text
          v-el-tooltip="'用户菜单'"
        >
          <el-icon><User /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              个人设置
            </el-dropdown-item>
            <el-dropdown-item command="help">
              <el-icon><QuestionFilled /></el-icon>
              帮助文档
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 移动端搜索框 -->
    <div v-if="showMobileSearch" class="mobile-search mobile-only">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索媒体资源..."
          class="search-input"
          @keyup.enter="handleSearch"
          autofocus
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #suffix>
            <el-button
              type="default"
              :icon="Close"
              text
              @click="showMobileSearch = false"
            />
          </template>
        </el-input>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore, useLayoutStore } from '@/stores'
import {
  Menu,
  Search,
  VideoPlay,
  Sunny,
  Moon,
  User,
  Setting,
  QuestionFilled,
  SwitchButton,
  Close
} from '@element-plus/icons-vue'

// 定义事件
const emit = defineEmits<{
  'toggle-sidebar': []
  'toggle-theme': []
  'search': [query: string]
}>()

const router = useRouter()
const authStore = useAuthStore()
const layoutStore = useLayoutStore()

// 响应式数据
const searchQuery = ref('')
const showMobileSearch = ref(false)

// 计算属性
const isDarkTheme = computed(() => {
  return layoutStore.isDarkTheme
})

// 搜索处理
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim())
    showMobileSearch.value = false
  }
}

// 用户菜单命令处理
const handleUserMenuCommand = (command: string) => {
  switch (command) {
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      window.open('/docs', '_blank')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
function handleLogout() {
  authStore.logout()
  router.push('/login')
}
</script>

<style scoped lang="scss">
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--topnav-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  z-index: var(--z-fixed);
  border-bottom: 1px solid var(--border-color);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition);

  &:hover {
    transform: scale(1.05);
  }

  i {
    color: var(--primary-color);
    font-size: 1.8rem;
  }
}

.nav-center {
  flex: 1;
  max-width: 500px;
  margin: 0 2rem;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  transition: var(--transition);

  &:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }

  i {
    color: var(--text-muted);
    margin-right: 0.75rem;
  }
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    outline: none;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-btn,
.user-btn,
.search-btn,
.menu-toggle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  transition: var(--transition);

  &:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px);
  }

  .el-icon {
    font-size: 1.4rem; // 增大图标尺寸，更好地填满容器
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 移动端搜索框
.mobile-search {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式
.mobile-only {
  @media (min-width: 769px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 768px) {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .top-nav {
    padding: 0 1rem;
  }

  .logo span {
    font-size: 1.2rem;
  }
}
</style>
