// 认证相关类型定义

export interface User {
  id: number
  username: string
  email?: string
  avatar?: string
  role?: 'admin' | 'user'
  is_active: boolean
  last_login?: string
  login_count: number
  created_at: string
  updated_at: string
}

export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  success: boolean
  message: string
  data?: {
    user: User
    token: string
    refreshToken?: string
    expiresIn: number
  }
  error?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'auto'

export interface AppSettings {
  theme: ThemeMode
  language: string
  notifications: boolean
}

// 修改密码请求类型
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
}
