/**
 * Element Plus 主题定制
 * 基于官方主题系统：https://element-plus.org/zh-CN/guide/theming.html
 */

// 导入Element Plus的SCSS变量
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // 主色系 - 紫色渐变主题
  $colors: (
    'primary': (
      'base': #8b5cf6,  // 主紫色
    ),
    'success': (
      'base': #10b981,  // 成功绿色
    ),
    'warning': (
      'base': #f59e0b,  // 警告橙色
    ),
    'danger': (
      'base': #ef4444,  // 错误红色
    ),
    'error': (
      'base': #ef4444,  // 错误红色
    ),
    'info': (
      'base': #64748b,  // 信息灰色
    ),
  ),
  
  // 文字颜色
  $text-color: (
    'primary': #f8fafc,     // 主要文字 - 浅色
    'regular': #e2e8f0,     // 常规文字 - 中等浅色
    'secondary': #94a3b8,   // 次要文字 - 灰色
    'placeholder': #64748b, // 占位符文字 - 深灰色
    'disabled': #475569,    // 禁用文字 - 更深灰色
  ),
  
  // 背景颜色
  $bg-color: (
    'page': #0f172a,        // 页面背景 - 深蓝黑
    '': #1e293b,            // 默认背景 - 深蓝灰
    'overlay': #334155,     // 遮罩背景 - 中等蓝灰
  ),
  
  // 边框颜色
  $border-color: (
    '': rgba(255, 255, 255, 0.1),        // 默认边框
    'light': rgba(255, 255, 255, 0.15),  // 浅色边框
    'lighter': rgba(255, 255, 255, 0.08), // 更浅边框
    'extra-light': rgba(255, 255, 255, 0.05), // 极浅边框
    'dark': rgba(255, 255, 255, 0.2),    // 深色边框
    'darker': rgba(255, 255, 255, 0.25), // 更深边框
  ),
  
  // 填充颜色
  $fill-color: (
    '': rgba(255, 255, 255, 0.08),       // 默认填充
    'light': rgba(255, 255, 255, 0.12),  // 浅色填充
    'lighter': rgba(255, 255, 255, 0.06), // 更浅填充
    'extra-light': rgba(255, 255, 255, 0.03), // 极浅填充
    'dark': rgba(255, 255, 255, 0.15),   // 深色填充
    'darker': rgba(255, 255, 255, 0.2),  // 更深填充
    'blank': transparent,                 // 透明填充
  ),
  
  // 圆角
  $border-radius: (
    'base': 8px,      // 基础圆角
    'small': 4px,     // 小圆角
    'round': 20px,    // 圆形
    'circle': 50%,    // 圆形百分比
  ),
  
  // 阴影
  $box-shadow: (
    '': (
      0 4px 12px rgba(0, 0, 0, 0.15),
      0 2px 6px rgba(0, 0, 0, 0.1),
    ),
    'light': (
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 4px rgba(0, 0, 0, 0.05),
    ),
    'base': (
      0 4px 12px rgba(0, 0, 0, 0.15),
      0 2px 6px rgba(0, 0, 0, 0.1),
    ),
    'dark': (
      0 8px 24px rgba(0, 0, 0, 0.25),
      0 4px 12px rgba(0, 0, 0, 0.15),
    ),
  ),
  
  // 字体
  $font-family: (
    '': '"Inter", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif',
  ),
  
  // 字体大小
  $font-size: (
    'extra-large': 20px,
    'large': 18px,
    'medium': 16px,
    'base': 14px,
    'small': 13px,
    'extra-small': 12px,
  ),
  
  // 组件尺寸
  $common-component-size: (
    'large': 40px,
    'default': 32px,
    'small': 24px,
  ),
  
  // 过渡动画
  $transition: (
    'all': all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    'fade': opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    'md-fade': (
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    ),
    'border': border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    'box-shadow': box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    'color': color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
  ),
  
  // z-index层级
  $z-index: (
    'normal': 1,
    'top': 1000,
    'popper': 2000,
  ),
);

// 明亮主题变量覆盖
[data-theme="light"] {
  // 重新定义明亮主题的颜色
  --el-text-color-primary: #1e293b;
  --el-text-color-regular: #334155;
  --el-text-color-secondary: #64748b;
  --el-text-color-placeholder: #94a3b8;
  --el-text-color-disabled: #cbd5e1;
  
  --el-bg-color-page: #f8fafc;
  --el-bg-color: #ffffff;
  --el-bg-color-overlay: #f1f5f9;
  
  --el-border-color: rgba(0, 0, 0, 0.1);
  --el-border-color-light: rgba(0, 0, 0, 0.08);
  --el-border-color-lighter: rgba(0, 0, 0, 0.06);
  --el-border-color-extra-light: rgba(0, 0, 0, 0.04);
  --el-border-color-dark: rgba(0, 0, 0, 0.15);
  --el-border-color-darker: rgba(0, 0, 0, 0.2);
  
  --el-fill-color: rgba(0, 0, 0, 0.04);
  --el-fill-color-light: rgba(0, 0, 0, 0.06);
  --el-fill-color-lighter: rgba(0, 0, 0, 0.03);
  --el-fill-color-extra-light: rgba(0, 0, 0, 0.02);
  --el-fill-color-dark: rgba(0, 0, 0, 0.08);
  --el-fill-color-darker: rgba(0, 0, 0, 0.12);
  
  --el-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05);
  --el-box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.03);
  --el-box-shadow-base: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05);
  --el-box-shadow-dark: 0 8px 24px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
}
