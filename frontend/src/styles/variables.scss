/**
 * CSS 变量定义
 * 包含颜色、尺寸、动画等全局样式变量
 */

:root {
  // 主色系
  --primary-color: #8b5cf6; // 主紫色
  --primary-hover: #7c3aed; // 悬停态
  --secondary-color: #06b6d4; // 青色
  --accent-color: #f59e0b; // 强调橙色

  // 功能性颜色
  --success-color: #10b981; // 成功绿
  --warning-color: #f59e0b; // 警告橙
  --error-color: #ef4444; // 错误红

  // 装饰性颜色
  --pink-accent: #ec4899;
  --purple-accent: #a855f7;
  --cyan-accent: #06b6d4;
  --orange-accent: #f97316;

  // 彩虹渐变
  --rainbow-gradient: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4, #10b981, #f59e0b, #ef4444);

  // 背景色系统 (深色主题)
  --bg-primary: #0f172a; // 页面主背景
  --bg-secondary: #1e293b; // 卡片面板背景
  --bg-tertiary: #334155; // 悬停状态背景
  --bg-glass: rgba(255, 255, 255, 0.1); // 毛玻璃效果
  --bg-card: rgba(255, 255, 255, 0.05); // 卡片半透明
  --bg-overlay: rgba(0, 0, 0, 0.5); // 遮罩层

  // 文字颜色系统
  --text-primary: #f8fafc; // 主要文字
  --text-secondary: #cbd5e1; // 次要文字
  --text-muted: #64748b; // 弱化文字
  --text-inverse: #1e293b; // 反色文字

  // 边框颜色
  --border-color: rgba(255, 255, 255, 0.1); // 标准边框
  --border-light: rgba(255, 255, 255, 0.2); // 亮色边框

  // 阴影系统
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05); // 小阴影
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1); // 中阴影
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1); // 大阴影
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1); // 超大阴影
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3); // 发光阴影

  // 尺寸系统
  --sidebar-width: 280px; // 侧边栏宽度
  --topnav-height: 64px; // 顶部导航高度
  --bottomnav-height: 64px; // 底部导航高度

  // 圆角系统
  --border-radius: 12px; // 标准圆角
  --border-radius-sm: 8px; // 小圆角
  --border-radius-lg: 16px; // 大圆角

  // 动画系统
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 标准过渡
  --transition-fast: all 0.15s ease; // 快速过渡
  --transition-slow: all 0.5s ease; // 慢速过渡
  --theme-transition-duration: 0.4s; // 主题切换动画时长

  // Z-index 层级系统
  --z-dropdown: 1000; // 下拉菜单
  --z-sticky: 1020; // 粘性定位
  --z-fixed: 1030; // 固定定位
  --z-modal-backdrop: 1040; // 模态框背景
  --z-modal: 1050; // 模态框内容
  --z-popover: 1060; // 弹出框
  --z-tooltip: 1070; // 工具提示
  --z-toast: 1080; // 消息提示

  // Toast 组件变量
  --p-toast-text-gap: 0.25rem; // Toast 文本间距
}

// 明亮主题变量覆盖
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-glass: rgba(0, 0, 0, 0.05);
  --bg-card: rgba(0, 0, 0, 0.02);
  --bg-overlay: rgba(255, 255, 255, 0.8);

  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --text-inverse: #f8fafc;

  --border-color: rgba(0, 0, 0, 0.1);
  --border-light: rgba(0, 0, 0, 0.05);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.2);
}

// 动态背景渐变效果
.gradient-bg {
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 25%,
    var(--purple-accent) 50%,
    var(--bg-secondary) 75%,
    var(--bg-primary) 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 主题切换背景动画 - 只针对背景和颜色变化
html,
body,
#app,
.login-container,
.login-card,
.p-card,
.p-card-content,
.p-card-header,
.form-input :deep(.p-inputtext),
.form-input :deep(.p-password .p-inputtext),
.login-button,
.theme-btn {
  transition:
    background-color var(--theme-transition-duration) ease,
    background var(--theme-transition-duration) ease,
    color var(--theme-transition-duration) ease,
    border-color var(--theme-transition-duration) ease;
}

// Element Plus CSS 变量覆盖
:root {
  // 主色系
  --el-color-primary: var(--primary-color);
  --el-color-primary-light-3: rgba(139, 92, 246, 0.7);
  --el-color-primary-light-5: rgba(139, 92, 246, 0.5);
  --el-color-primary-light-7: rgba(139, 92, 246, 0.3);
  --el-color-primary-light-8: rgba(139, 92, 246, 0.2);
  --el-color-primary-light-9: rgba(139, 92, 246, 0.1);
  --el-color-primary-dark-2: var(--primary-hover);

  // 成功色
  --el-color-success: var(--success-color);
  --el-color-success-light-3: rgba(16, 185, 129, 0.7);
  --el-color-success-light-5: rgba(16, 185, 129, 0.5);
  --el-color-success-light-7: rgba(16, 185, 129, 0.3);
  --el-color-success-light-8: rgba(16, 185, 129, 0.2);
  --el-color-success-light-9: rgba(16, 185, 129, 0.1);

  // 警告色
  --el-color-warning: var(--warning-color);
  --el-color-warning-light-3: rgba(245, 158, 11, 0.7);
  --el-color-warning-light-5: rgba(245, 158, 11, 0.5);
  --el-color-warning-light-7: rgba(245, 158, 11, 0.3);
  --el-color-warning-light-8: rgba(245, 158, 11, 0.2);
  --el-color-warning-light-9: rgba(245, 158, 11, 0.1);

  // 错误色
  --el-color-error: var(--error-color);
  --el-color-danger: var(--error-color);
  --el-color-error-light-3: rgba(239, 68, 68, 0.7);
  --el-color-error-light-5: rgba(239, 68, 68, 0.5);
  --el-color-error-light-7: rgba(239, 68, 68, 0.3);
  --el-color-error-light-8: rgba(239, 68, 68, 0.2);
  --el-color-error-light-9: rgba(239, 68, 68, 0.1);

  // 信息色
  --el-color-info: var(--text-muted);
  --el-color-info-light-3: rgba(100, 116, 139, 0.7);
  --el-color-info-light-5: rgba(100, 116, 139, 0.5);
  --el-color-info-light-7: rgba(100, 116, 139, 0.3);
  --el-color-info-light-8: rgba(100, 116, 139, 0.2);
  --el-color-info-light-9: rgba(100, 116, 139, 0.1);

  // 背景色
  --el-bg-color: var(--bg-primary);
  --el-bg-color-page: var(--bg-primary);
  --el-bg-color-overlay: var(--bg-secondary);

  // 文字色
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-text-color-secondary: var(--text-muted);
  --el-text-color-placeholder: var(--text-muted);
  --el-text-color-disabled: var(--text-muted);

  // 边框色
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color);
  --el-border-color-lighter: rgba(255, 255, 255, 0.05);
  --el-border-color-extra-light: rgba(255, 255, 255, 0.03);
  --el-border-color-dark: var(--border-light);
  --el-border-color-darker: var(--border-light);

  // 填充色
  --el-fill-color: var(--bg-card);
  --el-fill-color-light: var(--bg-glass);
  --el-fill-color-lighter: rgba(255, 255, 255, 0.03);
  --el-fill-color-extra-light: rgba(255, 255, 255, 0.02);
  --el-fill-color-dark: var(--bg-tertiary);
  --el-fill-color-darker: var(--bg-secondary);
  --el-fill-color-blank: transparent;

  // 圆角
  --el-border-radius-base: var(--border-radius);
  --el-border-radius-small: var(--border-radius-sm);
  --el-border-radius-round: 50%;
  --el-border-radius-circle: 50%;

  // 阴影
  --el-box-shadow: var(--shadow-md);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-base: var(--shadow-md);
  --el-box-shadow-dark: var(--shadow-lg);

  // 禁用透明度
  --el-disabled-opacity: 0.5;

  // 过渡动画
  --el-transition-duration: 0.3s;
  --el-transition-duration-fast: 0.15s;
}

// 明亮主题Element Plus变量覆盖
[data-theme="light"] {
  --el-bg-color: var(--bg-primary);
  --el-bg-color-page: var(--bg-primary);
  --el-bg-color-overlay: var(--bg-secondary);

  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-text-color-secondary: var(--text-muted);

  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color);
  --el-border-color-lighter: rgba(0, 0, 0, 0.05);
  --el-border-color-extra-light: rgba(0, 0, 0, 0.03);

  --el-fill-color: var(--bg-card);
  --el-fill-color-light: var(--bg-glass);
  --el-fill-color-lighter: rgba(0, 0, 0, 0.03);
  --el-fill-color-extra-light: rgba(0, 0, 0, 0.02);
}

// 通用效果类
// 毛玻璃效果
.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
}

// 彩虹文字效果
.rainbow-text {
  background: var(--rainbow-gradient);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbowShift 3s ease infinite;
}

@keyframes rainbowShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 发光按钮效果
.glow-button {
  position: relative;
  overflow: hidden;
  transition: var(--transition);

  // 光泽动画
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    &::before {
      left: 100%;
    }

    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
  }
}
