/**
 * 图标样式系统
 * 基于原型图设计的图标样式规范
 */

// 基础图标样式
.icon-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  color: inherit;
}

// 统计卡片图标 (48px, 渐变背景, 光泽动画)
.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--pink-accent));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  position: relative;
  overflow: hidden;
  
  .el-icon {
    font-size: 20px;
    z-index: 2;
  }
  
  // 光泽动画效果
  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
    z-index: 1;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
}

// 活动图标 (40px, 纯色背景)
.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  .el-icon {
    font-size: 16px;
  }
}

// 按钮图标 (24px, 圆形, 悬停效果)
.btn-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: var(--text-secondary);
  
  .el-icon {
    font-size: 11px;
  }
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    color: var(--text-primary);
    background: var(--bg-glass);
  }
  
  // 不同类型的按钮颜色
  &.primary {
    background: rgba(139, 92, 246, 0.9);
    color: white;
    
    &:hover {
      background: var(--primary-color);
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    }
  }
  
  &.success {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    
    &:hover {
      background: var(--success-color);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }
  }
  
  &.danger {
    background: rgba(239, 68, 68, 0.9);
    color: white;
    
    &:hover {
      background: var(--error-color);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }
  }
  
  &.warning {
    background: rgba(245, 158, 11, 0.9);
    color: white;
    
    &:hover {
      background: var(--warning-color);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }
  }
}

// 导航图标样式
.nav-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  transition: var(--transition);
  
  .el-icon {
    font-size: 1.2rem;
    transition: var(--transition);
  }
}

// 侧边栏导航图标
.sidebar-nav-icon {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-right: 1rem;
  transition: var(--transition);
  width: 1.5rem;
  text-align: center;
}

// 底部导航图标
.bottom-nav-icon {
  font-size: 1.2rem;
  transition: var(--transition);
}

// 顶部导航按钮图标
.top-nav-btn {
  width: 2.5rem !important;
  height: 2.5rem !important;
  border-radius: 50% !important;
  border: none !important;
  background: transparent !important;
  color: var(--text-secondary) !important;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 2.5rem !important;
  padding: 0 !important;
  
  &:hover {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    transform: translateY(-2px);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  .el-icon {
    font-size: 1.1rem;
  }
}

// Logo图标
.logo-icon {
  color: var(--primary-color);
  font-size: 1.8rem;
  transition: var(--transition);
  
  @media (max-width: 768px) {
    font-size: 1.6rem;
  }
}

// 空状态图标
.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
  color: var(--text-muted);
  
  .el-icon {
    font-size: 3rem;
  }
}

// 状态指示图标
.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  
  .el-icon {
    font-size: 0.75rem;
  }
  
  &.online {
    background: var(--success-color);
    color: white;
  }
  
  &.offline {
    background: var(--error-color);
    color: white;
  }
  
  &.warning {
    background: var(--warning-color);
    color: white;
  }
}

// 加载图标
.loading-icon {
  animation: spin 1s linear infinite;
  
  .el-icon {
    font-size: inherit;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式图标大小调整
@media (max-width: 480px) {
  .bottom-nav-icon {
    font-size: 1.1rem;
  }
  
  .nav-icon {
    width: 1.75rem;
    height: 1.75rem;
    
    .el-icon {
      font-size: 1.1rem;
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .nav-icon {
    width: 1.5rem;
    height: 1.5rem;
    
    .el-icon {
      font-size: 1rem;
    }
  }
  
  .bottom-nav-icon {
    font-size: 1rem;
  }
}
