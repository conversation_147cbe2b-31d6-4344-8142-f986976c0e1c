/**
 * 全局样式文件
 * 包含基础重置、工具类、组件样式覆盖等
 */
@use './theme.scss';
@use './layout.scss';
@use './navigation.scss';

// 基础样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px; // 1rem = 16px
  scroll-behavior: smooth;
}

body {
  // 系统字体栈
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

  // 颜色和背景
  background: var(--bg-primary);
  color: var(--text-primary);

  // 文字渲染优化
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // 布局控制
  overflow-x: hidden;

  // 移动端优化
  @media (max-width: 768px) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
}

// 应用容器
#app {
  min-height: 100vh;
  position: relative;
}

// 通用工具类
// 布局工具类
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

// 尺寸工具类
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

// 文本对齐工具类
.text-center {
  text-align: center;
}

// 定位工具类
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

// 显示控制工具类
.hidden {
  display: none;
}

// 响应式工具类
@media (max-width: 768px) {
  .md\:hidden {
    display: none;
  }

  .md\:block {
    display: block;
  }
}

@media (min-width: 769px) {
  .md\:flex {
    display: flex;
  }
}

// Vue 过渡动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// Element Plus 组件样式覆盖
// 按钮组件
.el-button {
  transition: var(--transition);
  border-radius: var(--border-radius) !important;
  font-weight: 500;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-text {
    background: transparent !important;
    border: none !important;

    &:hover {
      background: var(--bg-glass) !important;
    }
  }
}

// 输入框组件
.el-input {
  .el-input__wrapper {
    background: var(--bg-glass) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) !important;
    transition: var(--transition);
    box-shadow: none !important;

    &:hover {
      border-color: var(--border-light) !important;
    }

    &.is-focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2) !important;
      transform: translateY(-1px);
    }
  }

  .el-input__inner {
    color: var(--text-primary) !important;
    background: transparent !important;

    &::placeholder {
      color: var(--text-muted) !important;
    }
  }
}

// 卡片组件
.el-card {
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-light) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;
  backdrop-filter: blur(20px) !important;

  .el-card__header {
    background: transparent !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 2rem !important;
  }

  .el-card__body {
    background: transparent !important;
    padding: 2rem !important;
  }
}

// 下拉菜单组件
.el-dropdown-menu {
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--border-radius) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: var(--shadow-lg) !important;

  .el-dropdown-menu__item {
    color: var(--text-primary) !important;
    transition: var(--transition);

    &:hover {
      background: var(--bg-tertiary) !important;
      color: var(--primary-color) !important;
    }

    &.is-divided {
      border-top: 1px solid var(--border-color) !important;
    }
  }
}

// 图标组件
.el-icon {
  color: inherit;
  transition: var(--transition);
}

// 消息组件
.el-message {
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  backdrop-filter: blur(20px);
  color: var(--text-primary) !important;
  border-radius: var(--border-radius) !important;

  &.el-message--success {
    border-color: var(--success-color) !important;
  }

  &.el-message--warning {
    border-color: var(--warning-color) !important;
  }

  &.el-message--error {
    border-color: var(--error-color) !important;
  }
}

// 通知组件
.el-notification {
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  backdrop-filter: blur(20px);
  color: var(--text-primary) !important;
  border-radius: var(--border-radius) !important;

  .el-notification__title {
    color: var(--text-primary) !important;
  }

  .el-notification__content {
    color: var(--text-secondary) !important;
  }
}

// 对话框组件
.el-dialog {
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-lg) !important;

  .el-dialog__header {
    border-bottom: 1px solid var(--border-color);
  }

  .el-dialog__title {
    color: var(--text-primary) !important;
  }
}

.el-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px);
}

// 工具提示
.el-tooltip__popper {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius) !important;

  .el-popper__arrow::before {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
  }
}

// 加载组件
.el-loading-mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px);
}

.el-loading-spinner {
  .circular {
    stroke: var(--primary-color);
  }
}


// 浏览器样式覆盖
// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;

  &:hover {
    background: var(--text-muted);
  }
}

// 文本选择样式
::selection {
  background: rgba(139, 92, 246, 0.3);
  color: var(--text-primary);
}
