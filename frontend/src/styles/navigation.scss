/**
 * 导航样式
 * 包含顶部导航、侧边栏、底部导航的样式定义
 */

// 顶部导航样式
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--topnav-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  transition: var(--transition);

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

// Logo 样式
.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;

  &:hover {
    transform: scale(1.05);
  }

  i {
    color: var(--primary-color);
    font-size: 1.8rem;
  }

  @media (max-width: 768px) {
    font-size: 1.3rem;

    i {
      font-size: 1.6rem;
    }
  }
}

// 搜索框样式
.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  transition: var(--transition);
  backdrop-filter: blur(10px);

  &:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    background: var(--bg-glass);
  }

  i {
    color: var(--text-muted);
    margin-right: 0.75rem;
    font-size: 0.9rem;
  }
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 0.9rem;

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    outline: none;
  }
}

// 导航按钮样式
.nav-btn,
.theme-btn,
.user-btn,
.search-btn,
.menu-toggle {
  width: 2.5rem !important;
  height: 2.5rem !important;
  border-radius: 50% !important;
  border: none !important;
  background: transparent !important;
  color: var(--text-secondary) !important;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 2.5rem !important;
  padding: 0 !important;

  &:hover {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    transform: translateY(-2px);
  }

  &:active {
    transform: scale(0.95);
  }

  .el-icon {
    font-size: 1.1rem;
  }
}

// 侧边栏样式
.sidebar {
  position: fixed;
  top: var(--topnav-height);
  left: 0;
  width: var(--sidebar-width);
  height: calc(100vh - var(--topnav-height));
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  transform: translateX(-100%);
  transition: var(--transition);
  overflow-y: auto;
  overflow-x: hidden;

  &.open {
    transform: translateX(0);
  }

  @media (min-width: 769px) {
    transform: translateX(0);
  }

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;

    &:hover {
      background: var(--text-muted);
    }
  }
}

// 侧边栏菜单项
.sidebar-menu {
  list-style: none;
  padding: 1.5rem 0;
  margin: 0;
}

.sidebar-item {
  position: relative;
  margin: 0.25rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;

  &:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
  }

  &.active {
    background: linear-gradient(135deg, var(--primary-color), var(--purple-accent));
    color: white;
    box-shadow: var(--shadow-md);

    .sidebar-link {
      color: white;

      i {
        color: white;
      }
    }

    &::before {
      opacity: 1;
      transform: scaleY(1);
    }
  }

  // 激活指示器
  &::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%) scaleY(0);
    width: 4px;
    height: 60%;
    background: var(--accent-color);
    border-radius: 0 2px 2px 0;
    opacity: 0;
    transition: var(--transition);
  }
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);

  .el-icon {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-right: 1rem;
    transition: var(--transition);
    width: 1.5rem;
    text-align: center;
  }

  span {
    font-weight: 500;
    font-size: 0.95rem;
  }
}

// 底部导航样式
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottomnav-height);
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  padding: 0.5rem 0;

  // 安全区域适配
  padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  border-radius: var(--border-radius);
  min-width: 60px;
  position: relative;
  text-decoration: none;

  &:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
    transform: translateY(-2px);
  }

  &.active {
    color: var(--primary-color);

    .nav-icon {
      transform: translateY(-2px);
    }

    .nav-label {
      font-weight: 600;
    }

    &::after {
      opacity: 1;
      transform: translateX(-50%) scale(1);
    }
  }

  &:active {
    transform: scale(0.95);
  }

  // 激活指示器
  &::after {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%) scale(0);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition);
  }
}

.nav-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  transition: var(--transition);

  .el-icon {
    font-size: 1.2rem;
    transition: var(--transition);
  }
}

.nav-label {
  font-size: 0.75rem;
  font-weight: 500;
  transition: var(--transition);
  text-align: center;
  line-height: 1;
}

// 移动端搜索框
.mobile-search {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem;
  animation: slideDown 0.3s ease;
  z-index: var(--z-dropdown);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 侧边栏遮罩层
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  z-index: calc(var(--z-fixed) - 1);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式适配
@media (max-width: 480px) {
  .bottom-nav-item {
    min-width: 50px;
    padding: 0.4rem 0.5rem;
  }

  .nav-icon {
    width: 1.75rem;
    height: 1.75rem;

    .el-icon {
      font-size: 1.1rem;
    }
  }

  .nav-label {
    font-size: 0.7rem;
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .bottom-nav {
    height: 50px;
    padding: 0.25rem 0;
  }

  .bottom-nav-item {
    gap: 0.125rem;
    padding: 0.25rem 0.5rem;
  }

  .nav-icon {
    width: 1.5rem;
    height: 1.5rem;

    .el-icon {
      font-size: 1rem;
    }
  }

  .nav-label {
    font-size: 0.65rem;
  }
}
