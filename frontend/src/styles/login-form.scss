/**
 * 登录表单样式
 * 包含表单容器、输入框、按钮等组件的样式定义
 */

// 表单容器
.login-form {
  width: 100%;

  user-select: auto;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
}

// 表单组间距
.form-group {
  margin-bottom: 1.75rem;
}

// 错误文本样式
.error-text {
  color: var(--error-color);
  font-size: 1rem;
  margin-top: 0.5rem;
  display: block;
  font-weight: 500;
}

// 输入框样式
.form-input {
  width: 100%;

  // Element Plus 输入框
  :deep(.el-input__wrapper) {
    width: 100%;
    padding: 1rem !important;
    font-size: 1rem !important;
    border-radius: 14px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(255, 255, 255, 0.08) !important;
    box-shadow: none !important;

    // 明亮主题样式
    [data-theme="light"] & {
      border: 2px solid rgba(0, 0, 0, 0.08) !important;
      background: rgba(255, 255, 255, 0.6) !important;
    }

    // 聚焦状态
    &.is-focus {
      transform: translateY(-2px) !important;
      border-color: var(--primary-color) !important;
      background: rgba(255, 255, 255, 0.12) !important;
      box-shadow: 0 8px 32px rgba(139, 92, 246, 0.25),
      0 4px 16px rgba(0, 0, 0, 0.15),
      0 0 0 4px rgba(139, 92, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;

      // 明亮主题聚焦状态
      [data-theme="light"] & {
        background: rgba(255, 255, 255, 0.8) !important;
        box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.05),
        0 0 0 4px rgba(139, 92, 246, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
      }
    }

    &:hover {
      border-color: rgba(255, 255, 255, 0.25) !important;

      // 明亮主题悬停状态
      [data-theme="light"] & {
        border-color: rgba(0, 0, 0, 0.12) !important;
      }
    }
  }

  :deep(.el-input__inner) {
    color: #ffffff !important;
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;

    // 明亮主题文本颜色
    [data-theme="light"] & {
      color: #4a5568 !important;
      font-weight: 500 !important;
    }

    // 占位符样式
    &::placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
      font-weight: 400 !important;

      // 明亮主题占位符
      [data-theme="light"] & {
        color: rgba(0, 0, 0, 0.5) !important;
        font-weight: 400 !important;
      }
    }
  }

  // 前缀图标样式
  :deep(.el-input__prefix) {
    color: rgba(255, 255, 255, 0.8) !important;
    margin-right: 0.5rem;

    // 明亮主题图标颜色
    [data-theme="light"] & {
      color: rgba(0, 0, 0, 0.7) !important;
    }
  }

  // 后缀图标样式（密码显示/隐藏、清除按钮）
  :deep(.el-input__suffix) {
    color: rgba(255, 255, 255, 0.7) !important;

    // 明亮主题图标颜色
    [data-theme="light"] & {
      color: rgba(0, 0, 0, 0.6) !important;
    }

    .el-input__password {
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }
}

// 错误状态样式
.form-input.is-error {
  :deep(.el-input__wrapper) {
    border-color: var(--error-color) !important;

    &.is-focus {
      border-color: var(--error-color) !important;
      box-shadow: 0 8px 32px rgba(239, 68, 68, 0.25),
      0 4px 16px rgba(0, 0, 0, 0.15),
      0 0 0 4px rgba(239, 68, 68, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    }
  }
}

// 自定义提示条样式 - 毛玻璃风格
.custom-alert {
  margin-bottom: 1.5rem;
  // 添加入场动画
  animation: alertSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

// 错误提示条
.error-alert {
  .alert-content {
    display: flex;
    align-items: center;
    // 与登录卡片完全一致的毛玻璃效果
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(24px) saturate(150%);
    border: 1px solid rgba(255, 107, 107, 0.2);
    border-radius: var(--border-radius);
    padding: 0.75rem 0.875rem; // 缩小内边距

    // 与登录卡片相同的柔和阴影
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 2px rgba(255, 255, 255, 0.05),
    inset 0 -0.5px 1px rgba(0, 0, 0, 0.03);

    // 左侧红色指示条
    border-left: 3px solid rgba(255, 107, 107, 0.6);
  }

  .alert-icon {
    color: rgba(255, 107, 107, 0.9);
    font-size: 1rem; // 适中的图标尺寸
    margin-right: 0.625rem; // 缩小间距
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert-text {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
  }

  .alert-close {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    width: 1.2rem; // 缩小按钮尺寸
    height: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.625rem; // 缩小间距
    flex-shrink: 0;

    .el-icon {
      font-size: 0.7rem; // 缩小图标尺寸以匹配按钮
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 1);
      transform: scale(1.05);
      border-color: rgba(255, 255, 255, 0.25);
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.3);
      outline: none;
    }

    // 明亮主题样式
    [data-theme="light"] & {
      background: rgba(0, 0, 0, 0.08) !important; // 减少黑色强度
      border-color: rgba(0, 0, 0, 0.06) !important;
      color: rgba(0, 0, 0, 0.6) !important;

      &:hover {
        background: rgba(0, 0, 0, 0.15) !important; // 减少悬停时的黑色强度
        border-color: rgba(0, 0, 0, 0.1) !important;
        color: rgba(0, 0, 0, 0.8) !important;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 提示条入场动画
@keyframes alertSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 成功提示条
.success-alert {
  .alert-content {
    display: flex;
    align-items: center;
    // 与登录卡片完全一致的毛玻璃效果
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(24px) saturate(150%);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--border-radius);
    padding: 0.75rem 0.875rem; // 缩小内边距

    // 与登录卡片相同的柔和阴影
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 2px rgba(255, 255, 255, 0.05),
    inset 0 -0.5px 1px rgba(0, 0, 0, 0.03);

    // 左侧绿色指示条
    border-left: 3px solid rgba(16, 185, 129, 0.6);
  }

  .alert-icon {
    color: rgba(16, 185, 129, 0.9);
    font-size: 1rem; // 适中的图标尺寸
    margin-right: 0.625rem; // 缩小间距
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert-text {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
  }

  .alert-close {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    width: 1.2rem;
    height: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.625rem; // 缩小间距
    flex-shrink: 0;

    .el-icon {
      font-size: 0.7rem;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 1);
      transform: scale(1.05);
      border-color: rgba(255, 255, 255, 0.25);
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
      outline: none;
    }

    // 明亮主题样式
    [data-theme="light"] & {
      background: rgba(0, 0, 0, 0.08) !important;
      border-color: rgba(0, 0, 0, 0.06) !important;
      color: rgba(0, 0, 0, 0.6) !important;

      &:hover {
        background: rgba(0, 0, 0, 0.15) !important;
        border-color: rgba(0, 0, 0, 0.1) !important;
        color: rgba(0, 0, 0, 0.8) !important;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}


// 登录按钮样式
.login-button {
  width: 100% !important;
  background: linear-gradient(135deg, var(--primary-color), var(--purple-accent)) !important;
  border: none !important;
  padding: 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  border-radius: 14px !important;
  margin-top: 0.75rem;
  margin-bottom: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  height: auto !important;
  min-height: 2.75rem !important;
  // 多层阴影和内阴影
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3),
  0 4px 16px rgba(0, 0, 0, 0.1),
  inset 0 2px 0 rgba(255, 255, 255, 0.2),
  inset 0 -2px 0 rgba(0, 0, 0, 0.1) !important;

  // 图标样式
  .el-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
  }

  // 光泽动画效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // 悬停状态
  &:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--purple-accent)) !important;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 48px rgba(139, 92, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;

    &::before {
      left: 100%;
    }
  }

  // 点击状态
  &:active {
    transform: translateY(-1px) scale(1.01);
  }

  // 禁用状态
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  }
}

// 响应式设计
// 平板设备
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-input {
    :deep(.el-input__wrapper) {
      padding: 0.8rem !important;
      font-size: 0.9rem !important;
    }
  }

  .login-button {
    padding: 0.8rem !important;
    font-size: 0.9rem !important;
    width: 90% !important; // 平板端稍微增加宽度
  }
}

// 手机设备
@media (max-width: 480px) {
  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-input {
    :deep(.el-input__wrapper) {
      padding: 0.7rem !important;
      font-size: 0.85rem !important;
    }
  }

  .login-button {
    padding: 0.7rem !important;
    font-size: 0.85rem !important;
    margin-top: 0.5rem;
    width: 95% !important; // 手机端增加宽度以便点击
  }
}
