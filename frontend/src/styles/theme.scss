/**
 * 主题系统 - 统一的设计令牌和Element Plus主题定制
 * 包含颜色、尺寸、动画等全局样式变量
 */

// 导入Element Plus的SCSS变量
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // 主色系 - 紫色渐变主题
  $colors: (
    'primary': (
      'base': #8b5cf6,  // 主紫色
    ),
    'success': (
      'base': #10b981,  // 成功绿色
    ),
    'warning': (
      'base': #f59e0b,  // 警告橙色
    ),
    'danger': (
      'base': #ef4444,  // 错误红色
    ),
    'error': (
      'base': #ef4444,  // 错误红色
    ),
    'info': (
      'base': #64748b,  // 信息灰色
    ),
  ),

  // 文字颜色 - 增强对比度
  $text-color: (
    'primary': #ffffff,     // 主要文字 - 纯白
    'regular': #f1f5f9,     // 常规文字 - 浅灰白
    'secondary': #cbd5e1,   // 次要文字 - 中等灰
    'placeholder': #94a3b8, // 占位符文字 - 深灰色
    'disabled': #64748b,    // 禁用文字 - 更深灰色
  ),

  // 背景颜色 - 柔和深色
  $bg-color: (
    'page': #0a0f1c,        // 页面背景 - 更深蓝黑
    '': #1a202c,            // 默认背景 - 深蓝灰
    'overlay': #2d3748,     // 遮罩背景 - 中等蓝灰
  ),

  // 边框颜色
  $border-color: (
    '': rgba(255, 255, 255, 0.12),        // 默认边框
    'light': rgba(255, 255, 255, 0.18),   // 浅色边框
    'lighter': rgba(255, 255, 255, 0.08), // 更浅边框
    'extra-light': rgba(255, 255, 255, 0.05), // 极浅边框
    'dark': rgba(255, 255, 255, 0.25),    // 深色边框
    'darker': rgba(255, 255, 255, 0.3),   // 更深边框
  ),

  // 填充颜色
  $fill-color: (
    '': rgba(255, 255, 255, 0.06),       // 默认填充
    'light': rgba(255, 255, 255, 0.1),   // 浅色填充
    'lighter': rgba(255, 255, 255, 0.04), // 更浅填充
    'extra-light': rgba(255, 255, 255, 0.02), // 极浅填充
    'dark': rgba(255, 255, 255, 0.12),   // 深色填充
    'darker': rgba(255, 255, 255, 0.18), // 更深填充
    'blank': transparent,                 // 透明填充
  ),

  // 字体大小 - 增大基础字体
  $font-size: (
    'extra-large': 22px,
    'large': 20px,
    'medium': 18px,
    'base': 16px,      // 基础字体从14px增大到16px
    'small': 14px,     // 小字体从13px增大到14px
    'extra-small': 13px, // 超小字体从12px增大到13px
  ),

  // 组件尺寸 - 增大组件
  $common-component-size: (
    'large': 44px,     // 从40px增大到44px
    'default': 36px,   // 从32px增大到36px
    'small': 28px,     // 从24px增大到28px
  ),
);

:root {
  // 主色系
  --primary-color: #8b5cf6; // 主紫色
  --primary-hover: #7c3aed; // 悬停态
  --secondary-color: #06b6d4; // 青色
  --accent-color: #f59e0b; // 强调橙色

  // 功能性颜色
  --success-color: #10b981; // 成功绿
  --warning-color: #f59e0b; // 警告橙
  --error-color: #ef4444; // 错误红

  // 装饰性颜色
  --pink-accent: #ec4899;
  --purple-accent: #a855f7;
  --cyan-accent: #06b6d4;
  --orange-accent: #f97316;

  // 彩虹渐变
  --rainbow-gradient: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4, #10b981, #f59e0b, #ef4444);

  // 背景色系统 (深色主题) - 柔和调整
  --bg-primary: #0a0f1c; // 页面主背景 - 更深更柔和
  --bg-secondary: #1a202c; // 卡片面板背景 - 深蓝灰
  --bg-tertiary: #2d3748; // 悬停状态背景
  --bg-glass: rgba(255, 255, 255, 0.08); // 毛玻璃效果 - 降低透明度
  --bg-card: rgba(255, 255, 255, 0.04); // 卡片半透明 - 降低透明度
  --bg-overlay: rgba(0, 0, 0, 0.6); // 遮罩层

  // 文字颜色系统 - 增强对比度
  --text-primary: #ffffff; // 主要文字 - 纯白
  --text-secondary: #f1f5f9; // 次要文字 - 浅灰白
  --text-muted: #cbd5e1; // 弱化文字 - 提高对比度
  --text-inverse: #1e293b; // 反色文字

  // 边框颜色
  --border-color: rgba(255, 255, 255, 0.12); // 标准边框 - 增强可见性
  --border-light: rgba(255, 255, 255, 0.18); // 亮色边框

  // 阴影系统
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3);

  // 尺寸系统
  --sidebar-width: 280px;
  --topnav-height: 64px;
  --bottomnav-height: 64px;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;

  // 动画系统
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  // 字体系统 - 增大基础字体
  --font-size-xs: 13px;
  --font-size-sm: 14px;
  --font-size-base: 16px; // 从14px增大到16px
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 22px;
  --font-size-3xl: 24px;

  // 行高系统
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  // 间距系统
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

// 明亮主题变量覆盖
[data-theme="light"] {
  // Element Plus 明亮主题颜色
  --el-text-color-primary: #1a202c;
  --el-text-color-regular: #2d3748;
  --el-text-color-secondary: #4a5568;
  --el-text-color-placeholder: #718096;
  --el-text-color-disabled: #a0aec0;

  --el-bg-color-page: #f7fafc;
  --el-bg-color: #ffffff;
  --el-bg-color-overlay: #edf2f7;

  --el-border-color: rgba(0, 0, 0, 0.12);
  --el-border-color-light: rgba(0, 0, 0, 0.08);
  --el-border-color-lighter: rgba(0, 0, 0, 0.06);
  --el-border-color-extra-light: rgba(0, 0, 0, 0.04);
  --el-border-color-dark: rgba(0, 0, 0, 0.18);
  --el-border-color-darker: rgba(0, 0, 0, 0.25);

  --el-fill-color: rgba(0, 0, 0, 0.04);
  --el-fill-color-light: rgba(0, 0, 0, 0.06);
  --el-fill-color-lighter: rgba(0, 0, 0, 0.03);
  --el-fill-color-extra-light: rgba(0, 0, 0, 0.02);
  --el-fill-color-dark: rgba(0, 0, 0, 0.08);
  --el-fill-color-darker: rgba(0, 0, 0, 0.12);

  // 自定义变量 - 明亮主题
  --bg-primary: #f8fafc;        // 页面主背景 - 浅灰白
  --bg-secondary: #ffffff;      // 卡片面板背景 - 纯白
  --bg-tertiary: #f1f5f9;       // 悬停状态背景 - 浅灰
  --bg-glass: rgba(255, 255, 255, 0.8);  // 毛玻璃效果
  --bg-card: rgba(255, 255, 255, 0.9);   // 卡片半透明
  --bg-overlay: rgba(0, 0, 0, 0.1);      // 遮罩层

  --text-primary: #1a202c;     // 主要文字 - 深色
  --text-secondary: #2d3748;   // 次要文字 - 中等深色
  --text-muted: #4a5568;       // 弱化文字 - 灰色
  --text-inverse: #ffffff;     // 反色文字 - 白色

  --border-color: rgba(0, 0, 0, 0.12);   // 标准边框
  --border-light: rgba(0, 0, 0, 0.18);   // 亮色边框

  // 阴影系统 - 明亮主题
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.2);
}

// 全局字体设置和主题切换动画
* {
  font-size: var(--font-size-base);
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

// 基础文本样式
body {
  font-family: 'Inter', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

// 标题字体大小
h1 { font-size: var(--font-size-3xl); font-weight: 700; }
h2 { font-size: var(--font-size-2xl); font-weight: 600; }
h3 { font-size: var(--font-size-xl); font-weight: 600; }
h4 { font-size: var(--font-size-lg); font-weight: 500; }
h5 { font-size: var(--font-size-base); font-weight: 500; }
h6 { font-size: var(--font-size-sm); font-weight: 500; }
