/**
 * 登录页面样式
 * 包含页面布局、Logo动画、登录卡片等组件样式
 */

// 页面容器
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  // 禁用文本选择和右键菜单
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  // 柔和的渐变背景 - 深色主题
  background: linear-gradient(135deg, #0a0f1c 0%, #1a202c 50%, #2d3748 100%);

  // 明亮主题背景
  [data-theme="light"] & {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  }

  // 装饰性背景 - 更柔和的装饰
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      // 主要装饰光晕 - 深色主题
      radial-gradient(circle at 25% 75%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 75% 25%, rgba(236, 72, 153, 0.05) 0%, transparent 45%),
      radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.04) 0%, transparent 40%);
    background-size:
      100% 100%,
      100% 100%,
      100% 100%;
    pointer-events: none;
    z-index: 1;
    // 使用优雅的贝塞尔曲线动画
    animation: gentleFloat 30s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  }

  // 明亮主题装饰
  [data-theme="light"] &::before {
    background:
      // 明亮主题装饰光晕
      radial-gradient(circle at 25% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 25%, rgba(236, 72, 153, 0.06) 0%, transparent 45%),
      radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.05) 0%, transparent 40%);
  }
}

// 优雅的浮动动画，使用贝塞尔曲线
@keyframes gentleFloat {
  0%,
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateX(5px) translateY(-3px) scale(1.02);
  }
}

// 移除过度装饰，保持简洁

// 彩虹渐变动画
@keyframes rainbowShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// 主题切换按钮
.theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 100;

  user-select: auto;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
}

.theme-btn {
  background: rgba(255, 255, 255, 0.08) !important;
  backdrop-filter: blur(24px) saturate(180%) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  color: var(--text-primary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 1px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  .el-icon {
    font-size: 1.4rem; // 增大图标尺寸
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.theme-btn:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-3px);
  box-shadow:
    0 8px 32px rgba(139, 92, 246, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(139, 92, 246, 0.3) !important;
}

// 明亮主题的主题切换按钮
[data-theme="light"] .theme-btn {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  color: var(--text-primary) !important;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);

  &:hover {
    background: rgba(255, 255, 255, 0.95) !important;
    box-shadow:
      0 8px 32px rgba(139, 92, 246, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    border-color: rgba(139, 92, 246, 0.2) !important;
  }
}

// 登录内容区
.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 480px;
  animation: elegantEntrance 1.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 10;
}

@keyframes elegantEntrance {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Logo 区域
.logo-section {
  text-align: center;
  margin-bottom: 0;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  background: rgba(255, 255, 255, 0.01); // 进一步降低背景透明度
  border-bottom: 1px solid rgba(255, 255, 255, 0.04); // 减少边框透明度
  position: relative;
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.05),
    // 减少内阴影强度
    inset 0 -0.5px 1px rgba(0, 0, 0, 0.03);
}

.logo-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.05) 0%,
    rgba(236, 72, 153, 0.03) 50%,
    rgba(6, 182, 212, 0.02) 100%
  );
  pointer-events: none;
}

.brand-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  letter-spacing: 0.08em;
  line-height: 1;
  background: var(--rainbow-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: rainbowShift 8s ease-in-out infinite;
  position: relative;
  z-index: 1;
  cursor: pointer;
  transition: all 0.3s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2em;
  font-family: "SF Pro Display", "Segoe UI", system-ui, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

// Logo 文字样式
// Medi 单词 - 科技风格
.word-medi {
  color: #4f46e5;
  position: relative;
  font-weight: 800;
  text-shadow:
    0 0 20px rgba(79, 70, 229, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  animation: mediGlow 4s ease-in-out infinite;
}

.word-medi::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.2), transparent);
  border-radius: 4px;
  z-index: -1;
  opacity: 0;
  animation: mediSpark 3s ease-in-out infinite;
}

@keyframes mediGlow {
  0%,
  100% {
    text-shadow:
      0 0 20px rgba(79, 70, 229, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
  50% {
    text-shadow:
      0 0 30px rgba(79, 70, 229, 0.5),
      0 0 10px rgba(79, 70, 229, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

@keyframes mediSpark {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

// Paka 单词 - 可爱风格
.word-paka {
  color: #ec4899;
  position: relative;
  font-weight: 800;
  text-shadow:
    0 0 15px rgba(236, 72, 153, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.2);
  animation: pakaBounce 3s ease-in-out infinite;
  transform-origin: center bottom;
}

// 星星装饰
.word-paka::before {
  content: "✨";
  position: absolute;
  top: -0.4em;
  right: -0.3em;
  font-size: 0.4em;
  opacity: 0;
  animation: sparkle 2s ease-in-out infinite;
  filter: drop-shadow(0 0 5px rgba(236, 72, 153, 0.6));
}

.word-paka::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, transparent, rgba(236, 72, 153, 0.15), transparent);
  border-radius: 6px;
  z-index: -1;
  opacity: 0;
  animation: pakaGlow 4s ease-in-out infinite;
}

@keyframes pakaBounce {
  0%,
  100% {
    transform: translateY(0) scale(1);
    text-shadow:
      0 0 15px rgba(236, 72, 153, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
  25% {
    transform: translateY(-3px) scale(1.02);
    text-shadow:
      0 0 20px rgba(236, 72, 153, 0.6),
      0 0 8px rgba(236, 72, 153, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(0) scale(1);
    text-shadow:
      0 0 25px rgba(236, 72, 153, 0.5),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
  75% {
    transform: translateY(-1px) scale(1.01);
    text-shadow:
      0 0 18px rgba(236, 72, 153, 0.45),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8) rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.1) rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1) rotate(270deg);
  }
}

@keyframes pakaGlow {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@keyframes titleColorShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  33% {
    background-position: 30% 45%;
  }
  66% {
    background-position: 70% 55%;
  }
}

@keyframes titleColorShiftFast {
  0%,
  100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 25% 45%;
  }
  50% {
    background-position: 50% 55%;
  }
  75% {
    background-position: 75% 50%;
  }
}

@keyframes titlePulse {
  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  50% {
    transform: scale(1.01);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.25)) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15))
      drop-shadow(0 0 10px rgba(139, 92, 246, 0.2));
  }
}

.brand-subtitle {
  font-size: 1.1rem;
  margin: 0;
  font-weight: 400;
  position: relative;
  letter-spacing: 0.05em;
  line-height: 1.4;

  // 渐变文字效果
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(139, 92, 246, 0.8) 30%,
    rgba(236, 72, 153, 0.7) 60%,
    rgba(255, 255, 255, 0.8) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: subtitleShimmer 6s ease-in-out infinite;

  // 微妙的文字阴影
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));

  // 明亮主题样式
  [data-theme="light"] & {
    background: linear-gradient(135deg,
      rgba(74, 85, 104, 0.9) 0%,
      rgba(139, 92, 246, 0.8) 30%,
      rgba(236, 72, 153, 0.7) 60%,
      rgba(74, 85, 104, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05));
  }
  z-index: 1;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

// 登录卡片
.login-card {
  width: 100%;
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-card:hover {
  transform: translateY(-4px) scale(1.01);
}

.login-card:hover :deep(.el-card) {
  box-shadow:
    // 悬停时柔和增强阴影
    0 25px 80px rgba(0, 0, 0, 0.2),
    0 15px 50px rgba(0, 0, 0, 0.15),
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 4px 15px rgba(0, 0, 0, 0.1),
    // 轻微顶部高光
    inset 0 1px 2px rgba(255, 255, 255, 0.12),
    inset 0 0.5px 1px rgba(255, 255, 255, 0.18),
    // 底部阴影
    inset 0 -0.5px 1px rgba(0, 0, 0, 0.08),
    // 轻微发光边缘
    0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  transform: translateZ(0) translateY(-3px);
}

// 卡片背景装饰
.login-card::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.12) 0%,
    rgba(236, 72, 153, 0.1) 50%,
    rgba(6, 182, 212, 0.08) 100%);
  border-radius: 22px;
  z-index: -1;
  opacity: 0.8;
}

// 明亮主题的卡片背景装饰
[data-theme="light"] .login-card::before {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.15) 0%,
    rgba(236, 72, 153, 0.12) 50%,
    rgba(6, 182, 212, 0.1) 100%);
  opacity: 0.7;
}

.login-card :deep(.el-card) {
  background: rgba(255, 255, 255, 0.08) !important;
  backdrop-filter: blur(20px) saturate(120%) !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
  border-radius: 20px !important; // 增大圆角
  box-shadow:
    /* 柔和的外部阴影 */
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.08),
    /* 轻微的内部高光 */ inset 0 1px 2px rgba(255, 255, 255, 0.08),
    inset 0 0.5px 1px rgba(255, 255, 255, 0.12),
    /* 底部轻微阴影 */ inset 0 -0.5px 1px rgba(0, 0, 0, 0.05),
    /* 边缘轻微发光 */ 0 0 0 0.5px rgba(255, 255, 255, 0.06) !important;
  overflow: hidden;
  position: relative;
  transform: translateZ(0) translateY(-2px);
}

// 明亮主题登录卡片样式
[data-theme="light"] .login-card :deep(.el-card) {
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(16px) saturate(110%) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.04),
    0 4px 16px rgba(0, 0, 0, 0.02),
    0 2px 8px rgba(0, 0, 0, 0.01),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

.login-card :deep(.el-card__header) {
  padding: 0 !important;
  background: transparent !important;
  border-bottom: none !important;
}

.login-card :deep(.el-card__body) {
  padding: 2rem !important;
  background: transparent !important;
  position: relative;
  overflow: hidden;
}

.login-card :deep(.el-card__body)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.04) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.03) 0%, transparent 40%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  pointer-events: none;
  animation: gentleGlow 8s ease-in-out infinite;
}

// 明亮主题的卡片内部装饰
[data-theme="light"] .login-card :deep(.el-card__body)::before {
  background:
    radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.04) 0%, transparent 40%),
    linear-gradient(135deg, rgba(0, 0, 0, 0.01) 0%, rgba(0, 0, 0, 0.005) 100%);
}

// 副标题渐变动画
@keyframes subtitleShimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// 卡片内部微妙发光动画
@keyframes gentleGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式设计
// 平板设备
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-content {
    max-width: 100%;
  }

  .login-card :deep(.el-card__body) {
    padding: 1.5rem !important;
  }

  .logo-section {
    padding: 1.5rem 1.25rem 1.25rem 1.25rem;
  }

  .brand-title {
    font-size: 2.5rem;
    gap: 0.15em;
  }

  .brand-subtitle {
    font-size: 0.85rem;
  }

  .theme-toggle {
    top: 1rem;
    right: 1rem;
  }
}

// 手机设备
@media (max-width: 480px) {
  .login-container {
    padding: 0.75rem;
  }

  .login-content {
    max-width: 100%;
  }

  .login-card :deep(.el-card__body) {
    padding: 1.25rem !important;
  }

  .logo-section {
    padding: 1.25rem 1rem 1rem 1rem;
  }

  .brand-title {
    font-size: 2rem;
    gap: 0.1em;
  }

  .word-paka::before {
    font-size: 0.35em;
    top: -0.3em;
    right: -0.25em;
  }

  .brand-subtitle {
    font-size: 0.8rem;
  }

  .theme-toggle {
    top: 0.5rem;
    right: 0.5rem;
  }

  .theme-btn {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }
}
