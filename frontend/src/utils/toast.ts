/**
 * Toast 通知工具
 * 基于Element Plus的消息和通知组件
 */

import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'
import type { MessageOptions, NotificationOptions, ElMessageBoxOptions } from 'element-plus'

// 消息类型
export type MessageType = 'success' | 'warning' | 'info' | 'error'

// 默认配置
const defaultMessageOptions: Partial<MessageOptions> = {
  duration: 3000,
  showClose: true,
  center: false,
}

const defaultNotificationOptions: Partial<NotificationOptions> = {
  duration: 4500,
  showClose: true,
  position: 'top-right',
}

/**
 * 显示消息提示
 */
export const showMessage = {
  success: (message: string, options?: Partial<MessageOptions>) => {
    return ElMessage.success({
      ...defaultMessageOptions,
      message,
      ...options,
    })
  },
  
  warning: (message: string, options?: Partial<MessageOptions>) => {
    return ElMessage.warning({
      ...defaultMessageOptions,
      message,
      ...options,
    })
  },
  
  info: (message: string, options?: Partial<MessageOptions>) => {
    return ElMessage.info({
      ...defaultMessageOptions,
      message,
      ...options,
    })
  },
  
  error: (message: string, options?: Partial<MessageOptions>) => {
    return ElMessage.error({
      ...defaultMessageOptions,
      message,
      ...options,
    })
  },
}

/**
 * 显示通知
 */
export const showNotification = {
  success: (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return ElNotification.success({
      ...defaultNotificationOptions,
      title,
      message,
      ...options,
    })
  },
  
  warning: (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return ElNotification.warning({
      ...defaultNotificationOptions,
      title,
      message,
      ...options,
    })
  },
  
  info: (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return ElNotification.info({
      ...defaultNotificationOptions,
      title,
      message,
      ...options,
    })
  },
  
  error: (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return ElNotification.error({
      ...defaultNotificationOptions,
      title,
      message,
      ...options,
    })
  },
}

/**
 * 显示确认对话框
 */
export const showConfirm = async (
  message: string,
  title: string = '确认',
  options?: Partial<ElMessageBoxOptions>
): Promise<boolean> => {
  try {
    await ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      center: true,
      ...options,
    })
    return true
  } catch {
    return false
  }
}

/**
 * 显示提示对话框
 */
export const showAlert = async (
  message: string,
  title: string = '提示',
  options?: Partial<ElMessageBoxOptions>
): Promise<void> => {
  return ElMessageBox.alert(message, title, {
    confirmButtonText: '确定',
    type: 'info',
    center: true,
    ...options,
  })
}

/**
 * 显示输入对话框
 */
export const showPrompt = async (
  message: string,
  title: string = '输入',
  options?: Partial<ElMessageBoxOptions>
): Promise<string | null> => {
  try {
    const { value } = await ElMessageBox.prompt(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'text',
      center: true,
      ...options,
    })
    return value
  } catch {
    return null
  }
}

/**
 * 显示加载消息
 */
export const showLoading = (message: string = '加载中...') => {
  return ElMessage({
    message,
    type: 'info',
    duration: 0, // 不自动关闭
    showClose: false,
    icon: 'Loading',
  })
}

/**
 * 关闭所有消息
 */
export const closeAllMessages = () => {
  ElMessage.closeAll()
}

/**
 * 关闭所有通知
 */
export const closeAllNotifications = () => {
  ElNotification.closeAll()
}

// Toast 兼容性别名
export const toast = showMessage
export const notify = showNotification

// 导出默认对象
export default {
  message: showMessage,
  notification: showNotification,
  toast: showMessage,
  notify: showNotification,
  confirm: showConfirm,
  alert: showAlert,
  prompt: showPrompt,
  loading: showLoading,
  closeAllMessages,
  closeAllNotifications,
}
