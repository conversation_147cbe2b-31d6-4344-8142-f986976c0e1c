/**
 * 布局状态管理
 * 管理侧边栏、主题等布局相关状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLayoutStore = defineStore('layout', () => {
  // 状态
  const sidebarOpen = ref(false)
  const theme = ref<'light' | 'dark'>('dark')
  const isMobile = ref(false)

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const isLightTheme = computed(() => theme.value === 'light')

  // 操作方法
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }

  const openSidebar = () => {
    sidebarOpen.value = true
  }

  const closeSidebar = () => {
    sidebarOpen.value = false
  }

  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    applyTheme()
    saveTheme()
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    applyTheme()
    saveTheme()
  }

  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile
    // 移动端默认关闭侧边栏
    if (mobile) {
      sidebarOpen.value = false
    } else {
      // 桌面端默认打开侧边栏
      sidebarOpen.value = true
    }
  }

  // 应用主题到DOM
  const applyTheme = () => {
    document.documentElement.setAttribute('data-theme', theme.value)
  }

  // 保存主题到本地存储
  const saveTheme = () => {
    localStorage.setItem('medipaka-theme', theme.value)
  }

  // 从本地存储加载主题
  const loadTheme = () => {
    const savedTheme = localStorage.getItem('medipaka-theme') as 'light' | 'dark' | null
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      theme.value = savedTheme
    } else {
      // 默认使用系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      theme.value = prefersDark ? 'dark' : 'light'
    }
    applyTheme()
  }

  // 初始化布局
  const initLayout = () => {
    loadTheme()
    
    // 检查屏幕尺寸
    const checkScreenSize = () => {
      const mobile = window.innerWidth <= 768
      setMobile(mobile)
    }
    
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      // 只有在没有手动设置主题时才跟随系统
      if (!localStorage.getItem('medipaka-theme')) {
        setTheme(e.matches ? 'dark' : 'light')
      }
    }
    
    mediaQuery.addEventListener('change', handleSystemThemeChange)
    
    return () => {
      window.removeEventListener('resize', checkScreenSize)
      mediaQuery.removeEventListener('change', handleSystemThemeChange)
    }
  }

  return {
    // 状态
    sidebarOpen,
    theme,
    isMobile,
    
    // 计算属性
    isDarkTheme,
    isLightTheme,
    
    // 方法
    toggleSidebar,
    openSidebar,
    closeSidebar,
    toggleTheme,
    setTheme,
    setMobile,
    initLayout,
  }
})
