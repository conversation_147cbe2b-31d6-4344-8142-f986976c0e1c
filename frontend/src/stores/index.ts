import { useAuthStore } from './auth'
import { useLayoutStore } from './layout'

// 统一导出所有stores
export const stores = {
  auth: useAuthStore,
  layout: useLayoutStore,
}

// 单独导出每个store（保持向后兼容）
export { useAuthStore } from './auth'
export { useLayoutStore } from './layout'

// 一次性导入所有stores的函数
export const useStores = () => ({
  auth: useAuthStore(),
  layout: useLayoutStore(),
})

// 初始化所有stores
export const initializeStores = () => {
  const authStore = useAuthStore()
  const layoutStore = useLayoutStore()

  authStore.initAuth()
  layoutStore.initLayout()

  return {
    auth: authStore,
    layout: layoutStore,
  }
}
