import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { LoginCredentials, ThemeMode, User } from '@/types/auth'
import { authApi } from '@/api'
import { showMessage, showNotification } from '@/utils/toast'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 主题状态
  const theme = ref<ThemeMode>('dark')

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('auth_user')
    const savedTheme = localStorage.getItem('app_theme') as ThemeMode

    if (savedToken && savedUser) {
      token.value = savedToken
      try {
        user.value = JSON.parse(savedUser)
      } catch (e) {
        console.error('Failed to parse saved user data:', e)
        clearAuth()
      }
    }

    if (savedTheme) {
      theme.value = savedTheme
      applyTheme(savedTheme)
    } else {
      // 默认使用暗色主题
      theme.value = 'dark'
      applyTheme('dark')
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      // 调用真实的登录API
      const result = await authApi.login(credentials)

      if (result.success && result.data) {
        // 登录成功，保存用户信息和token
        user.value = result.data.user
        token.value = result.data.token

        // 保存到本地存储
        localStorage.setItem('auth_token', result.data.token)
        localStorage.setItem('auth_user', JSON.stringify(result.data.user))

        // 显示登录成功消息
        showNotification.success(
          '登录成功',
          `欢迎回来，${result.data.user.username}！`
        )

        return result
      } else {
        // 登录失败，设置错误信息
        error.value = result.message || result.error || '登录失败'
        return result
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : '登录失败，请稍后重试'
      error.value = message
      return {
        success: false,
        message,
        error: message,
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用登出API（清除本地存储）
      await authApi.logout()

      // 清除store状态
      user.value = null
      token.value = null
      refreshToken.value = null
      error.value = null

      // 显示登出成功消息
      showMessage.success('退出成功')
    } catch (err) {
      console.error('Logout error:', err)
      // 即使API调用失败，也要清除本地状态
      user.value = null
      token.value = null
      refreshToken.value = null
      error.value = null
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    logout()
  }

  // 主题切换
  const toggleTheme = () => {
    const newTheme = theme.value === 'dark' ? 'light' : 'dark'
    setTheme(newTheme)
  }

  const setTheme = (newTheme: ThemeMode) => {
    theme.value = newTheme
    localStorage.setItem('app_theme', newTheme)
    applyTheme(newTheme)
  }

  const applyTheme = (themeMode: ThemeMode) => {
    const root = document.documentElement

    if (themeMode === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light')
    } else {
      root.setAttribute('data-theme', themeMode)
    }
  }

  // 验证token有效性
  const verifyToken = async () => {
    if (!token.value) {
      return false
    }

    try {
      const result = await authApi.verifyToken()
      if (result.success && result.data) {
        // 更新用户信息
        user.value = result.data.user
        return true
      } else {
        // token无效，清除认证状态
        clearAuth()
        return false
      }
    } catch (err) {
      console.error('Token verification failed:', err)
      clearAuth()
      return false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    if (!token.value) {
      return null
    }

    try {
      const result = await authApi.getCurrentUser()
      if (result.success && result.data) {
        user.value = result.data
        localStorage.setItem('auth_user', JSON.stringify(result.data))
        return result.data
      }
      return null
    } catch (err) {
      console.error('Get current user failed:', err)
      return null
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    token,
    refreshToken,
    isLoading,
    error,
    theme,

    // 计算属性
    isAuthenticated,

    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    verifyToken,
    getCurrentUser,
    clearError,
    toggleTheme,
    setTheme,
  }
})
