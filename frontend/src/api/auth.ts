/**
 * 认证相关API服务
 * 提供登录、登出、用户信息等认证相关的API调用
 */

import { api } from './client'
import type {
  LoginCredentials,
  LoginResponse,
  User,
  ApiResponse,
  ChangePasswordRequest,
} from '@/types/auth'

// 认证API服务类
export class AuthApiService {
  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns 登录响应
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await api.post<{
        access_token: string
        token_type: string
        expires_in: number
        user: User
      }>('/api/auth/login', {
        username: credentials.username,
        password: credentials.password,
      })

      const { data } = response.data

      if (!data) {
        throw new Error('登录响应数据格式错误')
      }

      // 转换后端响应格式为前端期望的格式
      return {
        success: true,
        message: response.data.message || '登录成功',
        data: {
          user: data.user,
          token: data.access_token,
          expiresIn: data.expires_in,
        },
      }
    } catch (error) {
      console.error('Login API Error:', error)

      const message = error instanceof Error ? error.message : '登录失败，请稍后重试'

      return {
        success: false,
        message,
        error: message,
      }
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await api.get<User>('/api/auth/me')

      return {
        success: true,
        message: response.data.message || '获取用户信息成功',
        data: response.data.data!,
      }
    } catch (error) {
      console.error('Get Current User API Error:', error)

      const message = error instanceof Error ? error.message : '获取用户信息失败'

      return {
        success: false,
        message,
        error: message,
      }
    }
  }

  /**
   * 验证token有效性
   * @returns 验证结果
   */
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user: User }>> {
    try {
      const response = await api.get<{ valid: boolean; user: User }>('/api/auth/verify')

      return {
        success: true,
        message: response.data.message || '令牌验证成功',
        data: response.data.data!,
      }
    } catch (error) {
      console.error('Verify Token API Error:', error)

      const message = error instanceof Error ? error.message : '令牌验证失败'

      return {
        success: false,
        message,
        error: message,
      }
    }
  }

  /**
   * 修改密码
   * @param passwordData 密码修改数据
   * @returns 修改结果
   */
  async changePassword(passwordData: ChangePasswordRequest): Promise<ApiResponse> {
    try {
      const response = await api.post('/api/auth/change', {
        old_password: passwordData.oldPassword,
        new_password: passwordData.newPassword,
      })

      return {
        success: true,
        message: response.data.message || '密码修改成功',
      }
    } catch (error) {
      console.error('Change Password API Error:', error)

      const message = error instanceof Error ? error.message : '密码修改失败'

      return {
        success: false,
        message,
        error: message,
      }
    }
  }

  /**
   * 用户登出
   * 调用后端logout API并清除本地存储
   * @returns 登出结果
   */
  async logout(): Promise<ApiResponse> {
    try {
      // 调用后端logout API
      const response = await api.post('/api/auth/logout')

      // 无论API调用是否成功，都清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth_refresh_token')

      return {
        success: true,
        message: response.data.message || '登出成功',
      }
    } catch (error) {
      console.error('Logout Error:', error)

      // 即使API调用失败，也要清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth_refresh_token')

      return {
        success: true, // 即使API失败，本地登出仍然成功
        message: '登出成功',
      }
    }
  }
}

// 创建并导出认证API服务实例
export const authApi = new AuthApiService()

// 默认导出
export default authApi
