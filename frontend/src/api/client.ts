/**
 * HTTP客户端配置
 * 提供统一的API请求配置和拦截器
 */

import axios, {
  type AxiosInstance,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios'
import type { ApiResponse } from '@/types/auth'
import { showNotification } from '@/utils/toast'

// Toast消息常量
const ToastMessages = {
  AUTH_EXPIRED: '登录已过期，请重新登录',
  AUTH_INVALID: '认证信息无效，请重新登录',
  AUTH_REQUIRED: '用户名或密码错误',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  LOGOUT_SUCCESS: '退出成功'
}

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
const API_TIMEOUT = 30000 // 30秒超时

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 自动添加认证token
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = localStorage.getItem('auth_token')

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params,
      })
    }

    return config
  },
  error => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log(
        `✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          status: response.status,
          data: response.data,
        }
      )
    }

    return response
  },
  error => {
    // 统一错误处理
    console.error('❌ Response Error:', error)

    if (error.response) {
      const { status, data } = error.response

      // 处理401未授权错误
      if (status === 401) {
        const isLoginPage = window.location.pathname === '/login'
        const errorDetail = data?.detail || data?.message || '认证失败'

        // 清除本地存储的认证信息
        localStorage.removeItem('auth_token')
        localStorage.removeItem('auth_user')

        // 根据错误信息分类处理
        let errorMessage = errorDetail
        let shouldShowToast = !isLoginPage

        // 分析具体的401错误类型
        if (errorDetail.includes('令牌已失效') || errorDetail.includes('token')) {
          errorMessage = ToastMessages.AUTH_EXPIRED
        } else if (errorDetail.includes('用户名或密码错误')) {
          errorMessage = errorDetail
          shouldShowToast = false // 登录错误不显示Toast，在表单中显示
        } else if (errorDetail.includes('用户不存在')) {
          errorMessage = errorDetail
          shouldShowToast = false // 登录错误不显示Toast，在表单中显示
        } else if (errorDetail.includes('认证信息无效') || errorDetail.includes('无效')) {
          errorMessage = ToastMessages.AUTH_INVALID
        } else {
          errorMessage = ToastMessages.AUTH_REQUIRED
        }

        // 如果不是登录页面，显示Toast并重定向
        if (!isLoginPage && shouldShowToast) {
          showNotification.error('认证失败', errorMessage)

          // 延迟重定向，让用户看到Toast消息
          setTimeout(() => {
            window.location.href = '/login'
          }, 1000)
        }

        return Promise.reject(new Error(errorMessage))
      }

      // 处理其他HTTP错误
      const errorMessage = data?.detail || data?.message || `请求失败 (${status})`

      // 显示Toast消息（服务器错误）
      if (status >= 500) {
        showNotification.error('服务器错误', ToastMessages.SERVER_ERROR)
      }

      return Promise.reject(new Error(errorMessage))
    } else if (error.request) {
      // 网络错误
      showNotification.error('网络错误', ToastMessages.NETWORK_ERROR)
      return Promise.reject(new Error(ToastMessages.NETWORK_ERROR))
    } else {
      // 其他错误
      const errorMessage = error.message || '请求失败'
      showNotification.error('请求失败', errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
  }
)

// 导出配置好的axios实例
export default apiClient

// 导出一些常用的请求方法
export const api = {
  get: <T = any>(url: string, config?: InternalAxiosRequestConfig) =>
    apiClient.get<ApiResponse<T>>(url, config),

  post: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig) =>
    apiClient.post<ApiResponse<T>>(url, data, config),

  put: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig) =>
    apiClient.put<ApiResponse<T>>(url, data, config),

  delete: <T = any>(url: string, config?: InternalAxiosRequestConfig) =>
    apiClient.delete<ApiResponse<T>>(url, config),

  patch: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig) =>
    apiClient.patch<ApiResponse<T>>(url, data, config),
}

// 导出类型
export type { InternalAxiosRequestConfig, AxiosResponse }
