import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由配置
 * 定义应用的所有路由规则
 */
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      requiresAuth: false,
      title: '登录 - MediPaka',
    },
  },
  // 主布局路由
  {
    path: '/',
    component: () => import('@/layout/MainLayout.vue'),
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          requiresAuth: true,
          title: '仪表盘 - MediPaka',
        },
      },
      {
        path: 'subscriptions',
        name: 'Subscriptions',
        component: () => import('@/views/Subscriptions.vue'),
        meta: {
          requiresAuth: true,
          title: '订阅管理 - MediPaka',
        },
      },
      {
        path: 'downloads',
        name: 'Downloads',
        component: () => import('@/views/Downloads.vue'),
        meta: {
          requiresAuth: true,
          title: '下载队列 - MediPaka',
        },
      },
      {
        path: 'search',
        name: 'Search',
        component: () => import('@/views/Search.vue'),
        meta: {
          requiresAuth: true,
          title: '资源搜索 - MediPaka',
        },
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: {
          requiresAuth: true,
          title: '系统设置 - MediPaka',
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到 - MediPaka',
    },
  },
]
