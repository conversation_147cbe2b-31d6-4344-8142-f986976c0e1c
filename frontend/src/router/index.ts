import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores'
import { routes } from './routes'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 如果有token但需要验证，先验证token有效性
  if (authStore.token && to.meta.requiresAuth) {
    const isValid = await authStore.verifyToken()
    if (!isValid) {
      // token无效，重定向到登录页
      next({
        name: 'Login',
        query: { redirect: to.fullPath },
      })
      return
    }
  }

  // 检查认证状态
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，重定向到登录页
    next({
      name: 'Login',
      query: { redirect: to.fullPath },
    })
  } else if (to.name === 'Login' && authStore.isAuthenticated) {
    // 已登录用户访问登录页，重定向到仪表盘
    next({ name: 'Dashboard' })
  } else {
    next()
  }
})

export default router
