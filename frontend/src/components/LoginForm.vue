<template>
  <form class="login-form" @submit.prevent="handleSubmit">
    <!-- 用户名输入 -->
    <div class="form-group">
      <el-input
        v-model="form.username"
        placeholder="用户名"
        :class="{ 'is-error': errors.username }"
        class="form-input"
        autocomplete="username"
        clearable
        @input="handleInputChange"
      >
        <template #prefix>
          <el-icon><User /></el-icon>
        </template>
      </el-input>
      <div v-if="errors.username" class="error-text">
        {{ errors.username }}
      </div>
    </div>

    <!-- 密码输入 -->
    <div class="form-group">
      <el-input
        v-model="form.password"
        type="password"
        placeholder="密码"
        :class="{ 'is-error': errors.password }"
        class="form-input"
        show-password
        clearable
        autocomplete="current-password"
        @input="handleInputChange"
      >
        <template #prefix>
          <el-icon><Lock /></el-icon>
        </template>
      </el-input>
      <div v-if="errors.password" class="error-text">
        {{ errors.password }}
      </div>
    </div>

    <!-- 自定义错误提示条 -->
    <div v-if="authStore.error" class="custom-alert error-alert">
      <div class="alert-content">
        <el-icon class="alert-icon"><WarningFilled /></el-icon>
        <span class="alert-text">{{ getDisplayError() }}</span>
        <button class="alert-close" type="button" @click="authStore.clearError">
          <el-icon><Close /></el-icon>
        </button>
      </div>
    </div>

    <!-- 登录按钮 -->
    <el-button
      type="primary"
      native-type="submit"
      :loading="authStore.isLoading"
      class="login-button"
      size="large"
    >
      <el-icon><Right /></el-icon>
      登录
    </el-button>
  </form>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useAuthStore } from '@/stores'
import { User, Lock, WarningFilled, Close, Right } from '@element-plus/icons-vue'

const emit = defineEmits<{
  loginSuccess: []
}>()

const authStore = useAuthStore()

// 表单数据
const form = reactive({
  username: '',
  password: '',
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: '',
})

// 输入变化处理
const handleInputChange = () => {
  // 用户开始输入时清除登录错误
  if (authStore.error) {
    authStore.clearError()
  }

  // 清除字段验证错误
  errors.username = ''
  errors.password = ''
}

// 表单验证
const validateForm = (): boolean => {
  // 清除之前的错误
  errors.username = ''
  errors.password = ''

  let isValid = true

  if (!form.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (form.username.length < 3) {
    errors.username = '用户名至少3个字符'
    isValid = false
  }

  if (!form.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (form.password.length < 4) {
    errors.password = '密码至少4个字符'
    isValid = false
  }

  return isValid
}

// 获取显示的错误信息
const getDisplayError = () => {
  const { error } = authStore
  if (!error) return ''

  // 过滤掉不应该在登录表单中显示的错误
  if (error.includes('登录已过期') || error.includes('认证信息无效')) {
    return '请重新登录'
  }

  return error
}

// 提交表单
const handleSubmit = async () => {
  // 清除之前的错误
  authStore.clearError()

  // 验证表单
  if (!validateForm()) {
    return
  }

  const result = await authStore.login(form)

  if (result.success) {
    // 清除任何之前的错误
    authStore.clearError()
    emit('loginSuccess')
  } else {
    // 错误信息已经在store中设置，会自动显示在自定义提示条中
    console.error('Login failed:', result.error)
  }
}
</script>

<style scoped>
@import '@/styles/login-form.scss';
</style>
