<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div>
        <h1 class="page-title rainbow-text">仪表盘</h1>
        <p class="page-subtitle">系统运行状态总览</p>
      </div>
      <div class="page-actions">
        <el-button
          type="default"
          :icon="Refresh"
          @click="refreshData"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="card-grid cols-4 mb-4">
      <div class="stats-card">
        <div class="stat-icon">
          <el-icon><Download /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stats-value">{{ stats.totalDownloads }}</div>
          <div class="stats-label">总下载数</div>
          <div class="stats-change positive">
            <el-icon><ArrowUp /></el-icon>
            <span>+12%</span>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stat-icon">
          <el-icon><Rss /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stats-value">{{ stats.activeSubscriptions }}</div>
          <div class="stats-label">活跃订阅</div>
          <div class="stats-change positive">
            <el-icon><ArrowUp /></el-icon>
            <span>+5%</span>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stat-icon">
          <el-icon><Odometer /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stats-value">{{ stats.downloadSpeed }}</div>
          <div class="stats-label">下载速度</div>
          <div class="stats-change neutral">
            <el-icon><Minus /></el-icon>
            <span>0%</span>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stat-icon">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stats-value">{{ stats.storageUsed }}</div>
          <div class="stats-label">存储使用</div>
          <div class="stats-change negative">
            <el-icon><ArrowDown /></el-icon>
            <span>-2%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="card-grid cols-2">
      <!-- 最近活动 -->
      <div class="content-card">
        <div class="card-header">
          <h3 class="card-title">最近活动</h3>
          <el-button type="default" text class="btn-icon">
            <el-icon><Link /></el-icon>
          </el-button>
        </div>
        <div class="card-content">
          <div v-if="recentActivities.length === 0" class="empty-state">
            <div class="empty-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="empty-title">暂无活动</div>
            <div class="empty-description">最近没有下载或订阅活动</div>
          </div>
          <div v-else class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统状态 -->
      <div class="content-card">
        <div class="card-header">
          <h3 class="card-title">系统状态</h3>
          <div class="status-indicator online">
            <div class="status-dot"></div>
            <span>正常运行</span>
          </div>
        </div>
        <div class="card-content">
          <div class="status-list">
            <div class="status-item">
              <span class="status-label">下载器状态</span>
              <span class="status-value success">正常</span>
            </div>
            <div class="status-item">
              <span class="status-label">API 连接</span>
              <span class="status-value success">已连接</span>
            </div>
            <div class="status-item">
              <span class="status-label">磁盘空间</span>
              <span class="status-value warning">75% 已用</span>
            </div>
            <div class="status-item">
              <span class="status-label">内存使用</span>
              <span class="status-value success">45% 已用</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  Refresh,
  ArrowUp,
  ArrowDown,
  Minus,
  Link,
  Clock,
  Download,
  Plus,
  VideoPlay,
  Rss,
  Odometer,
  FolderOpened
} from '@element-plus/icons-vue'

// 响应式数据
const stats = ref({
  totalDownloads: 1234,
  activeSubscriptions: 56,
  downloadSpeed: '12.5 MB/s',
  storageUsed: '2.1 TB'
})

const recentActivities = ref([
  {
    id: 1,
    title: '《进击的巨人》第四季 下载完成',
    time: '2分钟前',
    icon: Download
  },
  {
    id: 2,
    title: '新增订阅：《鬼灭之刃》',
    time: '15分钟前',
    icon: Plus
  },
  {
    id: 3,
    title: '《蜘蛛侠：纵横宇宙》开始下载',
    time: '1小时前',
    icon: VideoPlay
  }
])

// 刷新数据
const refreshData = () => {
  // 模拟数据刷新
  console.log('刷新数据...')
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped lang="scss">
// 统计卡片样式
.stats-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--purple-accent));
  }
}

.stat-content {
  flex: 1;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  transition: var(--transition);

  &:hover {
    background: var(--bg-tertiary);
  }
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-size: 1rem;
  }
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.85rem;
  color: var(--text-muted);
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }
}

.status-label {
  color: var(--text-secondary);
}

.status-value {
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;

  &.success {
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
  }

  &.warning {
    color: var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
  }

  &.error {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;

  &.online {
    color: var(--success-color);
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
}
</style>
