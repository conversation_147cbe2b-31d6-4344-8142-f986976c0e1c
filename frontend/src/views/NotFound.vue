<template>
  <div class="not-found-container gradient-bg">
    <div class="not-found-card glass-effect">
      <div class="error-icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">页面未找到</p>
      <el-button type="primary" class="back-btn glow-button" @click="$router.push('/dashboard')">
        返回首页
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WarningFilled } from '@element-plus/icons-vue'
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.not-found-card {
  text-align: center;
  padding: 3rem;
  border-radius: var(--border-radius-lg);
  max-width: 400px;
}

.error-icon {
  font-size: 4rem;
  color: var(--warning-color);
  margin-bottom: 1rem;
}

.error-title {
  font-size: 4rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.error-message {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.back-btn {
  background: linear-gradient(45deg, var(--primary-color), var(--purple-accent)) !important;
  border: none !important;
}
</style>
