<template>
  <div class="page-container">
    <div class="page-header">
      <div>
        <h1 class="page-title">Element Plus 组件演示</h1>
        <p class="page-subtitle">展示按需导入和自动导入的Element Plus组件</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" @click="showNotificationDemo">
          <el-icon><Bell /></el-icon>
          通知演示
        </el-button>
        <el-button type="success" @click="showMessageDemo">
          <el-icon><ChatDotRound /></el-icon>
          消息演示
        </el-button>
      </div>
    </div>

    <!-- 基础组件演示 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="12">
        <el-card class="demo-card">
          <template #header>
            <div class="card-header">
              <span>按钮组件</span>
              <el-button type="text" @click="buttonDemo">
                <el-icon><View /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="demo-section">
            <el-space wrap>
              <el-button type="primary">主要按钮</el-button>
              <el-button type="success">成功按钮</el-button>
              <el-button type="warning">警告按钮</el-button>
              <el-button type="danger">危险按钮</el-button>
              <el-button type="info">信息按钮</el-button>
            </el-space>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="demo-card">
          <template #header>
            <div class="card-header">
              <span>输入组件</span>
              <el-button type="text" @click="inputDemo">
                <el-icon><Edit /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="demo-section">
            <el-space direction="vertical" style="width: 100%">
              <el-input v-model="demoInput" placeholder="请输入内容">
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-input v-model="demoPassword" type="password" placeholder="请输入密码" show-password />
            </el-space>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据展示组件 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card class="demo-card">
          <template #header>
            <div class="card-header">
              <span>数据表格</span>
              <el-button type="text" @click="tableDemo">
                <el-icon><Grid /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="demo-section">
            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="name" label="名称" width="180" />
              <el-table-column prop="type" label="类型" width="180" />
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '正常' ? 'success' : 'warning'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button size="small" @click="handleEdit(scope.$index, scope.row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 反馈组件演示 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="demo-card">
          <template #header>
            <span>进度条</span>
          </template>
          <div class="demo-section">
            <el-progress :percentage="progressValue" />
            <el-progress :percentage="progressValue" type="circle" />
            <el-button @click="increaseProgress">增加进度</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="demo-card">
          <template #header>
            <span>评分组件</span>
          </template>
          <div class="demo-section">
            <el-rate v-model="ratingValue" />
            <p>当前评分: {{ ratingValue }}</p>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="demo-card">
          <template #header>
            <span>开关组件</span>
          </template>
          <div class="demo-section">
            <el-switch v-model="switchValue" />
            <p>状态: {{ switchValue ? '开启' : '关闭' }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 对话框演示 -->
    <el-dialog v-model="dialogVisible" title="演示对话框" width="500">
      <p>这是一个使用Element Plus的对话框组件</p>
      <p>具有毛玻璃效果和现代化设计</p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 演示页面，无需特殊逻辑
</script>

<style scoped lang="scss">
ul {
  padding-left: 1.5rem;

  li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
  }
}

h4 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}
</style>
