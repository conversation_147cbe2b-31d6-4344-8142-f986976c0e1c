<template>
  <div class="login-container" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
    <!-- 主题切换按钮 -->
    <div class="theme-toggle">
      <el-button
        type="default"
        :icon="authStore.theme === 'dark' ? Sunny : Moon"
        text
        circle
        class="theme-btn"
        @click="authStore.toggleTheme"
      />
    </div>

    <!-- 登录内容 -->
    <div class="login-content">
      <!-- 登录卡片 -->
      <el-card class="login-card">
        <template #header>
          <!-- Logo区域 -->
          <div class="logo-section">
            <h1 class="brand-title rainbow-text">MediPaka</h1>
            <p class="brand-subtitle">智能媒体自动化管理平台</p>
          </div>
        </template>

        <LoginForm @login-success="handleLoginSuccess" />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import LoginForm from '@/components/LoginForm.vue'
import { <PERSON>, <PERSON> } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const handleLoginSuccess = () => {
  // 获取重定向路径
  const redirect = router.currentRoute.value.query.redirect as string
  router.push(redirect || '/dashboard')
}
</script>

<style scoped>
@import '@/styles/login.scss';
</style>
