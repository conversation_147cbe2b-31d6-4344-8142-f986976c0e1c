## 📱 页面功能

### 登录页面 (Login)

- 用户名密码登录

### 🏠 仪表盘 (Dashboard)

- 系统统计概览
- 下载趋势图表
- 最近活动记录
- 快速操作入口

### 榜单推荐 (Recommend)

- TMDB热门电影
- TMDB热门电视剧
- 豆瓣热门电影
- 豆瓣热门电视剧

### 📡 订阅管理 (Subscriptions)

- TMDB榜单订阅
- 豆瓣榜单订阅
- Mikan RSS动漫订阅
- 手动添加订阅
- 订阅状态管理
- 关键词过滤设置

### 📥 下载队列 (Downloads)

- 下载任务列表
- 实时进度监控
- 下载速度显示
- 任务控制操作

### 🔍 资源搜索 (Search)

- 多源资源搜索
- 搜索结果展示
- 一键下载功能
- 搜索源状态管理

### 🛠️ 系统设置 (Settings)

- 账号配置
- 重命名规则
- 代理配置
- 下载器配置
- TMDB配置
- 通知配置
- Emby配置
- 关于系统

## 🎨 主题特性

- 🌙 支持明暗主题切换 (light/dark/auto)
- 📱 完整的移动端支持，独立的移动端布局
- 🎯 现代化UI设计，TypeScript类型安全
- ⚡ 流畅的动画效果和触摸反馈
- 🔧 高度可定制，响应式设计
- 📐 移动端适配：安全区域、触摸优化
- 🔄 自动设备检测和路由重定向

## 📱 移动端特性

### 🎨 移动端UI优化

- **底部导航栏**: 符合移动端使用习惯的底部Tab导航
- **侧边抽屉**: 左侧滑出的导航抽屉，包含完整菜单
- **触摸优化**: 按钮点击反馈、防止双击缩放、触摸高亮
- **安全区域**: 支持iPhone刘海屏等异形屏幕

### 📐 响应式设计

- 自动检测: 自动检测设备类型并重定向到对应界面
- 断点系统: xs/sm/md/lg/xl 五级响应式断点
- 方向适配: 支持横屏/竖屏自动适配
- UI动画: 使用animejs
