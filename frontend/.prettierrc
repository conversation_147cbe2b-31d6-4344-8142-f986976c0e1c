{"$schema": "https://json.schemastore.org/prettierrc", "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": false, "endOfLine": "auto", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": "*.vue", "options": {"parser": "vue", "vueIndentScriptAndStyle": false, "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "ignore", "bracketSpacing": true}}, {"files": "*.{js,jsx,ts,tsx}", "options": {"bracketSpacing": true}}, {"files": "*.{scss,sass}", "options": {"parser": "scss", "singleQuote": false, "bracketSpacing": true}}, {"files": "*.css", "options": {"parser": "css", "singleQuote": false, "bracketSpacing": true}}, {"files": "*.json", "options": {"parser": "json", "trailingComma": "none", "bracketSpacing": false}}, {"files": "*.md", "options": {"parser": "markdown", "proseWrap": "preserve", "printWidth": 100, "htmlWhitespaceSensitivity": "ignore"}}, {"files": "*.{yml,yaml}", "options": {"parser": "yaml", "bracketSpacing": true, "singleQuote": false}}]}