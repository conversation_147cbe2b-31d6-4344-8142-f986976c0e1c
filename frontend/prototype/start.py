#!/usr/bin/env python3
"""
MediPaka 原型图本地服务器启动脚本
用于快速启动本地HTTP服务器预览原型图
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def find_free_port(start_port=8080):
    """查找可用端口"""
    import socket
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def start_server():
    """启动本地服务器"""
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 查找可用端口
    port = find_free_port()
    if not port:
        print("❌ 无法找到可用端口")
        return
    
    # 创建服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print("🚀 MediPaka 原型图服务器启动成功!")
            print(f"📍 本地地址: http://localhost:{port}")
            print(f"📱 演示页面: http://localhost:{port}/demo.html")
            print(f"🎨 完整原型: http://localhost:{port}/index.html")
            print("\n💡 使用说明:")
            print("   • 在浏览器中访问上述地址")
            print("   • 按 Ctrl+C 停止服务器")
            print("   • 调整浏览器窗口大小查看响应式效果")
            print("   • 点击右上角图标切换主题")
            print("\n🎯 功能特色:")
            print("   ✓ 现代化毛玻璃设计")
            print("   ✓ 完整响应式布局")
            print("   ✓ 明暗主题切换")
            print("   ✓ 实时数据更新")
            print("   ✓ 微交互动画")
            print("\n" + "="*50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/demo.html')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️  请手动在浏览器中打开上述地址")
            
            print("="*50)
            print("🔄 服务器运行中... (按 Ctrl+C 停止)")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
        print("感谢使用 MediPaka 原型图演示!")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")

def show_help():
    """显示帮助信息"""
    print("🎨 MediPaka 原型图启动器")
    print("\n用法:")
    print("  python start.py        # 启动服务器")
    print("  python start.py -h     # 显示帮助")
    print("\n功能:")
    print("  • 自动查找可用端口")
    print("  • 自动打开浏览器")
    print("  • 提供演示页面和完整原型")
    print("\n文件说明:")
    print("  demo.html    - 演示介绍页面")
    print("  index.html   - 完整原型图")
    print("  styles.css   - 样式文件")
    print("  script.js    - 交互脚本")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        return
    
    print("🎨 MediPaka 原型图启动器")
    print("=" * 30)
    
    # 检查文件是否存在
    required_files = ['index.html', 'styles.css', 'script.js', 'demo.html']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   • {file}")
        print("\n请确保所有原型图文件都在当前目录中")
        return
    
    print("✅ 所有文件检查完成")
    print("🚀 正在启动服务器...")
    print()
    
    start_server()

if __name__ == "__main__":
    main()
