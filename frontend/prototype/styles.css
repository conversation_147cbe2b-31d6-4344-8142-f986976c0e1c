/* CSS变量系统 */
:root {
  /* 年轻化活力颜色系统 */
  --primary-color: #8b5cf6;
  --primary-hover: #7c3aed;
  --secondary-color: #06b6d4;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --pink-accent: #ec4899;
  --purple-accent: #a855f7;
  --cyan-accent: #06b6d4;
  --orange-accent: #f97316;

  /* 背景色 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-card: rgba(255, 255, 255, 0.05);

  /* 文字颜色 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;

  /* 边框和阴影 */
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* 尺寸 */
  --sidebar-width: 280px;
  --topnav-height: 64px;
  --bottomnav-height: 64px;
  --border-radius: 12px;
  --border-radius-sm: 8px;

  /* 动画 */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 明亮主题 */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-glass: rgba(0, 0, 0, 0.05);
  --bg-card: rgba(0, 0, 0, 0.02);

  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #94a3b8;

  --border-color: rgba(0, 0, 0, 0.1);
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 50%,
    var(--bg-primary) 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 主容器 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

/* 顶部导航栏 */
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--topnav-height);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  z-index: 1000;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.menu-toggle:hover {
  background: var(--bg-glass);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(45deg, var(--primary-color), var(--pink-accent), var(--cyan-accent));
  background-size: 200% 200%;
  animation: gradientText 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gradientText {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.nav-center {
  flex: 1;
  max-width: 600px;
  margin: 0 24px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-box i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.theme-toggle:hover {
  background: var(--bg-glass);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-glass);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.user-menu:hover {
  background: var(--bg-card);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

/* 侧边栏导航 */
.sidebar {
  position: fixed;
  left: 0;
  top: var(--topnav-height);
  width: var(--sidebar-width);
  height: calc(100vh - var(--topnav-height));
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  padding: 24px 0;
  z-index: 900;
  transition: var(--transition);
}

.nav-menu {
  list-style: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
}

.nav-item:hover {
  background: var(--bg-glass);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
  position: relative;
}

.nav-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.nav-item i {
  width: 20px;
  text-align: center;
}

/* 主内容区域 */
.main-content {
  margin-left: var(--sidebar-width);
  margin-top: var(--topnav-height);
  padding: 24px;
  min-height: calc(100vh - var(--topnav-height));
}

/* 页面样式 */
.page {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.page-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--pink-accent));
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--pink-accent));
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--cyan-accent), var(--purple-accent));
  opacity: 0;
  transition: opacity 0.3s;
}

.btn-secondary:hover::before {
  opacity: 0.1;
}

.btn-secondary:hover {
  background: var(--bg-card);
  transform: translateY(-3px) scale(1.05);
  border-color: var(--cyan-accent);
  box-shadow: 0 8px 20px rgba(6, 182, 212, 0.3);
}

.btn-icon {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 8px;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
}

.btn-icon:hover {
  background: var(--bg-glass);
  color: var(--text-primary);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--pink-accent));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
}

.stat-content h3 {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  color: var(--text-muted);
}

.stat-change.positive {
  color: var(--success-color);
}

/* 仪表盘网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.dashboard-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.activity-item:hover {
  background: var(--bg-glass);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-content h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.activity-content p {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill.completed {
  background: var(--success-color);
}

.progress-fill.downloading {
  background: linear-gradient(90deg, var(--primary-color), var(--cyan-accent));
}

.progress-fill.paused {
  background: var(--warning-color);
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 状态指示器 */
.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.downloading {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary-color);
}

.status.completed {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

/* 系统状态 */
.system-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.status-item:last-child {
  border-bottom: none;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
}

.status-indicator.online {
  background: var(--success-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-indicator.offline {
  background: var(--error-color);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-indicator.online::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 标签页 */
.tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 12px 16px;
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  font-weight: 500;
  position: relative;
}

.tab:hover {
  color: var(--text-primary);
  background: var(--bg-glass);
}

.tab.active {
  color: var(--primary-color);
  background: var(--bg-glass);
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
}

/* 底部导航 */
.bottom-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottomnav-height);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--border-color);
  padding: 8px;
  z-index: 1000;
}

.bottom-nav .nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  transition: var(--transition);
}

.bottom-nav .nav-item i {
  font-size: 20px;
}

.bottom-nav .nav-item.active {
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    margin-bottom: var(--bottomnav-height);
    padding: 16px;
  }

  .bottom-nav {
    display: flex;
  }

  .nav-center {
    display: none;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-nav {
    padding: 0 16px;
  }

  .main-content {
    padding: 12px;
  }
}

/* 订阅管理样式 */
/* 订阅筛选器 */
.subscription-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.filter-select {
  padding: 6px 10px;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition);
  min-width: 100px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.filter-select:hover {
  border-color: var(--primary-color);
}

.subscription-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.subscription-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  display: flex;
  gap: 12px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  align-items: stretch;
  height: 180px;
}

/* 筛选隐藏的卡片 */
.subscription-card.hidden {
  display: none;
}

.subscription-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--cyan-accent),
    var(--purple-accent)
  );
  background-size: 200% 100%;
  animation: gradientMove 4s ease infinite;
}

/* 不同状态的卡片样式 */
.subscription-card.completed::before {
  background: linear-gradient(90deg, var(--success-color), #22c55e);
}

.subscription-card.downloading::before {
  background: linear-gradient(90deg, var(--primary-color), var(--cyan-accent));
}

.subscription-card.paused::before {
  background: linear-gradient(90deg, var(--warning-color), #f97316);
}

.subscription-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 15px 35px rgba(16, 185, 129, 0.2);
}

.subscription-card:hover::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.subscription-poster {
  position: relative;
  flex-shrink: 0;
  width: 100px;
  height: 100%;
}

.subscription-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
}

.subscription-source {
  position: absolute;
  top: 4px;
  right: 4px;
  background: var(--primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.subscription-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 0;
  height: 100%;
  justify-content: space-between;
}

/* 订阅卡片头部布局 */
.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.subscription-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  margin-right: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 订阅信息区域 */
.subscription-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  margin-bottom: 4px;
}

/* 信息行布局 */
.info-row {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  min-height: 20px;
  margin-bottom: 3px;
}

/* 包含进度条的行居中对齐 */
.info-row.progress-row {
  align-items: center;
}

.info-label {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 36px;
  flex-shrink: 0;
  line-height: 1.4;
  padding-top: 1px;
}

.info-value {
  font-size: 11px;
  color: var(--text-primary);
  font-weight: 400;
  line-height: 1.4;
  flex: 1;
}

/* 任务状态显示 */
.subscription-status {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  width: fit-content;
  line-height: 1.2;
}

.subscription-status.completed {
  color: var(--success-color);
}

.subscription-status.downloading {
  color: var(--primary-color);
}

.subscription-status.paused {
  color: var(--warning-color);
}

.subscription-status i {
  font-size: 11px;
}

/* 评分显示 */
.subscription-rating {
  display: flex;
  align-items: center;
  gap: 3px;
  background: rgba(245, 158, 11, 0.9);
  color: white;
  padding: 4px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  flex-shrink: 0;
}

.subscription-rating i {
  font-size: 9px;
}

/* 标签样式 */
.subscription-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  align-items: flex-start;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 1px 3px;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: var(--primary-color);
  border-radius: 4px;
  font-size: 8px;
  font-weight: 500;
  line-height: 1.2;
  transition: var(--transition);
}

.tag:hover {
  background: rgba(139, 92, 246, 0.2);
}

/* 进度条在info-row中的样式 */
.info-row .subscription-progress {
  flex: 1;
  margin-left: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.progress-info span {
  font-size: 10px;
  color: var(--text-secondary);
}

.progress-percent {
  font-weight: 600;
  color: var(--primary-color);
}

/* 操作按钮区域 - 进度条下方，右对齐 */
.subscription-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-end;
  margin-top: 6px;
  padding-top: 4px;
  flex-shrink: 0;
}

/* 小图标按钮样式 */
.subscription-card .btn-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.subscription-card .btn-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 不同类型按钮的颜色 */
.subscription-card .btn-icon.primary {
  background: rgba(139, 92, 246, 0.9);
  color: white;
}

.subscription-card .btn-icon.primary:hover {
  background: var(--primary-color);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.subscription-card .btn-icon.success {
  background: rgba(34, 197, 94, 0.9);
  color: white;
}

.subscription-card .btn-icon.success:hover {
  background: var(--success-color);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
}

.subscription-card .btn-icon.secondary {
  background: rgba(100, 116, 139, 0.9);
  color: white;
}

.subscription-card .btn-icon.secondary:hover {
  background: var(--secondary-color);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.4);
}

.subscription-card .btn-icon.danger {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.subscription-card .btn-icon.danger:hover {
  background: var(--error-color);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.subscription-card .btn-icon.warning {
  background: rgba(245, 158, 11, 0.9);
  color: white;
}

.subscription-card .btn-icon.warning:hover {
  background: var(--warning-color);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* 编辑弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.btn-secondary,
.btn-primary {
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-glass);
  color: var(--text-primary);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #5b21b6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 下载队列样式 */
.download-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-glass);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.download-stats .stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.download-stats .stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.download-stats .stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.download-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.download-item {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition);
}

.download-item:hover {
  background: var(--bg-card);
}

.download-item.completed {
  border-color: var(--success-color);
}

.download-info {
  flex: 1;
}

.download-info h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.download-info p {
  font-size: 12px;
  color: var(--text-secondary);
}

.download-progress {
  flex: 2;
  margin: 0 16px;
}

.progress-info {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.download-actions {
  display: flex;
  gap: 8px;
}

/* 搜索页面样式 */
.search-form {
  margin-bottom: 32px;
}

.search-input-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-select {
  padding: 8px 12px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.search-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition);
}

.result-item:hover {
  background: var(--bg-card);
  transform: translateY(-2px);
}

.result-info {
  flex: 1;
}

.result-info h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.result-info p {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.result-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.result-meta span {
  padding: 2px 8px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.quality {
  background: var(--primary-color) !important;
  color: white !important;
}

.result-actions {
  display: flex;
  gap: 8px;
}

/* 设置页面样式 */
.settings-content {
  max-width: 800px;
}

.settings-section {
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-primary);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 移动端适配补充 */
@media (max-width: 768px) {
  .subscription-grid {
    grid-template-columns: 1fr;
  }

  .subscription-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    height: auto;
    padding: 16px;
    align-items: center;
  }

  .subscription-poster {
    align-self: center;
    width: 80px;
    height: 120px;
  }

  .subscription-poster img {
    width: 100%;
    height: 100%;
  }

  .subscription-header {
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
  }

  .subscription-header h3 {
    margin-right: 0;
    text-align: center;
  }

  .subscription-info {
    align-items: flex-start;
    text-align: left;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-label {
    min-width: auto;
  }

  .subscription-tags {
    justify-content: flex-start;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
  }

  .subscription-actions {
    justify-content: center;
    margin-top: 12px;
    gap: 8px;
  }

  .subscription-card .btn-icon {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .subscription-filters {
    flex-direction: column;
    gap: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    width: 100%;
  }

  .filter-select {
    width: 100%;
    min-width: auto;
  }

  .download-stats {
    flex-direction: column;
    gap: 16px;
  }

  .download-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-filters {
    justify-content: center;
  }

  .result-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .result-actions {
    justify-content: center;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 80px;
  right: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 300px;
  max-width: 400px;
  z-index: 2000;
  animation: slideInRight 0.3s ease-out;
  box-shadow: var(--shadow-lg);
}

.notification.success {
  border-color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.notification.warning {
  border-color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
}

.notification.error {
  border-color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
}

.notification i:first-child {
  color: var(--primary-color);
  font-size: 18px;
}

.notification.success i:first-child {
  color: var(--success-color);
}

.notification.warning i:first-child {
  color: var(--warning-color);
}

.notification.error i:first-child {
  color: var(--error-color);
}

.notification span {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: var(--transition);
}

.notification-close:hover {
  background: var(--bg-glass);
  color: var(--text-primary);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: var(--transition);
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-state p {
  font-size: 14px;
  margin-bottom: 20px;
}

/* 骨架屏加载 */
.skeleton {
  background: linear-gradient(90deg, var(--bg-card) 25%, var(--bg-glass) 50%, var(--bg-card) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-sm);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

/* 视图切换按钮 */
.view-toggle {
  display: flex;
  background: var(--bg-glass);
  border-radius: var(--border-radius);
  padding: 4px;
  gap: 4px;
}

.view-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-btn:hover {
  color: var(--text-primary);
  background: var(--bg-card);
}

.view-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* 下载卡片网格 */
.download-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.download-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.download-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--pink-accent), var(--cyan-accent));
  background-size: 200% 100%;
  animation: gradientMove 3s ease infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.download-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.download-card.completed::before {
  background: linear-gradient(90deg, var(--success-color), var(--cyan-accent));
}

.download-poster {
  position: relative;
  margin-bottom: 12px;
}

.download-poster img {
  width: 80px;
  height: 120px;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
  float: left;
  margin-right: 12px;
}

.download-overlay {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.download-type {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.completed-badge {
  background: var(--success-color);
  color: white;
  padding: 4px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.download-content h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.download-episode {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 2px;
}

.download-meta {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.download-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
}

.speed-text {
  font-size: 11px;
  color: var(--text-secondary);
}

.eta-text {
  font-size: 10px;
  color: var(--text-muted);
  margin-top: 4px;
}

/* 搜索结果卡片 */
.search-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.search-result-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.search-result-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--cyan-accent),
    var(--purple-accent),
    var(--orange-accent)
  );
  background-size: 200% 100%;
  animation: gradientMove 4s ease infinite;
}

.search-result-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 25px 50px rgba(6, 182, 212, 0.3);
}

.result-poster {
  position: relative;
  width: 100%;
  height: 320px;
  overflow: hidden;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.result-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.search-result-card:hover .result-poster img {
  transform: scale(1.05);
}

.result-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.quality-badge {
  background: rgba(139, 92, 246, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.rating-badge {
  background: rgba(245, 158, 11, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(10px);
}

.rating-badge i {
  font-size: 10px;
}

/* 悬停操作按钮 */
.hover-actions {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.search-result-card:hover .hover-actions {
  opacity: 1;
  pointer-events: all;
  transform: translateX(-50%) translateY(-8px);
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.action-btn:hover::before {
  transform: scale(1);
}

.download-btn {
  background: rgba(139, 92, 246, 0.9);
}

.download-btn:hover {
  background: var(--primary-color);
  transform: scale(1.1);
}

.cloud-btn {
  background: rgba(6, 182, 212, 0.9);
}

.cloud-btn:hover {
  background: var(--cyan-accent);
  transform: scale(1.1);
}

.subscribe-btn {
  background: rgba(16, 185, 129, 0.9);
}

.subscribe-btn:hover {
  background: var(--success-color);
  transform: scale(1.1);
}

.info-btn {
  background: rgba(100, 116, 139, 0.9);
}

.info-btn:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
}

.result-content {
  padding: 16px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.result-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  flex: 1;
  margin-right: 8px;
}

.result-year {
  background: linear-gradient(135deg, var(--primary-color), var(--pink-accent));
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.result-episode {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 12px;
  font-weight: 500;
}

.result-info {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
}

.info-item i {
  font-size: 11px;
  color: var(--text-muted);
}

.result-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.tag.quality {
  background: rgba(139, 92, 246, 0.2);
  color: var(--primary-color);
}

.tag.source {
  background: rgba(6, 182, 212, 0.2);
  color: var(--cyan-accent);
}

.tag.format {
  background: rgba(245, 158, 11, 0.2);
  color: var(--orange-accent);
}

/* 列表视图样式 */
.result-item-list {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.result-item-list::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--primary-color), var(--pink-accent), var(--cyan-accent));
  background-size: 100% 200%;
  animation: gradientMove 3s ease infinite;
}

.result-item-list:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
}

.list-poster {
  position: relative;
  flex-shrink: 0;
}

.list-poster img {
  width: 70px;
  height: 105px;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
}

.list-overlay {
  position: absolute;
  top: 4px;
  right: 4px;
}

.list-content {
  flex: 1;
  min-width: 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.list-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 12px;
  line-height: 1.3;
}

.list-year {
  background: linear-gradient(135deg, var(--primary-color), var(--pink-accent));
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.list-episode {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.list-info {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.list-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.list-actions .action-btn {
  width: 36px;
  height: 36px;
  font-size: 13px;
}

/* 视图切换动画 */
.card-view,
.list-view {
  display: none;
  animation: fadeInUp 0.3s ease-out;
}

.card-view.active,
.list-view.active {
  display: grid;
}

.list-view.active {
  display: flex;
  flex-direction: column;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端通知适配 */
@media (max-width: 768px) {
  .notification {
    right: 16px;
    left: 16px;
    min-width: auto;
    max-width: none;
  }

  .download-grid,
  .search-results-grid {
    grid-template-columns: 1fr;
  }

  .view-toggle {
    order: -1;
    margin-bottom: 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .result-poster {
    height: 280px;
  }

  .result-item-list {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .list-poster {
    align-self: center;
  }

  .list-poster img {
    width: 80px;
    height: 120px;
  }

  .list-header {
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
  }

  .list-info {
    justify-content: center;
  }

  .list-actions {
    justify-content: center;
    margin-top: 12px;
  }

  .hover-actions {
    position: static;
    opacity: 1;
    pointer-events: all;
    transform: none;
    margin-top: 12px;
    justify-content: center;
  }
}
