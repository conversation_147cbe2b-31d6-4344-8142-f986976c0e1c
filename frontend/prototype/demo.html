<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MediPaka 原型图演示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 50%, #06b6d4 100%);
        background-size: 400% 400%;
        animation: gradientShift 8s ease infinite;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .demo-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 40px;
        max-width: 800px;
        width: 100%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      .logo {
        font-size: 48px;
        background: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4);
        background-size: 200% 200%;
        animation: gradientText 3s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 20px;
      }

      @keyframes gradientText {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      h1 {
        font-size: 32px;
        color: #1e293b;
        margin-bottom: 16px;
        font-weight: 700;
      }

      .subtitle {
        font-size: 18px;
        color: #64748b;
        margin-bottom: 40px;
        line-height: 1.6;
      }

      .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }

      .feature {
        background: rgba(99, 102, 241, 0.1);
        border-radius: 12px;
        padding: 20px;
        border: 1px solid rgba(99, 102, 241, 0.2);
      }

      .feature-icon {
        font-size: 24px;
        color: #6366f1;
        margin-bottom: 12px;
      }

      .feature h3 {
        font-size: 16px;
        color: #1e293b;
        margin-bottom: 8px;
      }

      .feature p {
        font-size: 14px;
        color: #64748b;
        line-height: 1.5;
      }

      .demo-button {
        display: inline-block;
        background: linear-gradient(135deg, #8b5cf6, #ec4899);
        color: white;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        position: relative;
        overflow: hidden;
      }

      .demo-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
      }

      .demo-button:hover::before {
        left: 100%;
      }

      .demo-button:hover {
        background: linear-gradient(135deg, #7c3aed, #db2777);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 12px 30px rgba(139, 92, 246, 0.5);
      }

      .instructions {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        border-radius: 12px;
        padding: 20px;
        margin-top: 30px;
        text-align: left;
      }

      .instructions h3 {
        color: #10b981;
        margin-bottom: 12px;
        font-size: 16px;
      }

      .instructions ul {
        list-style: none;
        padding: 0;
      }

      .instructions li {
        color: #1e293b;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        font-size: 14px;
      }

      .instructions li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #10b981;
        font-weight: bold;
      }

      .tech-stack {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }

      .tech-stack h3 {
        color: #1e293b;
        margin-bottom: 16px;
        font-size: 18px;
      }

      .tech-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
      }

      .tech-tag {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid rgba(99, 102, 241, 0.2);
      }

      @media (max-width: 768px) {
        .demo-container {
          padding: 30px 20px;
        }

        h1 {
          font-size: 28px;
        }

        .subtitle {
          font-size: 16px;
        }

        .features {
          grid-template-columns: 1fr;
        }
      }
    </style>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="demo-container">
      <div class="logo">
        <i class="fas fa-play-circle"></i>
      </div>

      <h1>MediPaka 原型图演示</h1>
      <p class="subtitle">
        现代化媒体自动化系统的前端界面原型图
        <br />
        展示完整的用户界面设计和交互体验
      </p>

      <div class="features">
        <div class="feature">
          <div class="feature-icon">
            <i class="fas fa-palette"></i>
          </div>
          <h3>现代化设计</h3>
          <p>毛玻璃效果、微交互动画、一致的设计语言</p>
        </div>

        <div class="feature">
          <div class="feature-icon">
            <i class="fas fa-mobile-alt"></i>
          </div>
          <h3>响应式布局</h3>
          <p>完美适配桌面端和移动端，自动设备检测</p>
        </div>

        <div class="feature">
          <div class="feature-icon">
            <i class="fas fa-moon"></i>
          </div>
          <h3>主题切换</h3>
          <p>明暗主题无缝切换，支持系统跟随</p>
        </div>

        <div class="feature">
          <div class="feature-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <h3>实时更新</h3>
          <p>动态进度条、状态监控、模拟数据更新</p>
        </div>
      </div>

      <a href="index.html" class="demo-button">
        <i class="fas fa-rocket"></i>
        查看完整原型图
      </a>

      <div class="instructions">
        <h3>
          <i class="fas fa-info-circle"></i>
          使用说明
        </h3>
        <ul>
          <li>点击侧边栏或底部导航切换不同页面</li>
          <li>点击右上角图标切换明暗主题</li>
          <li>调整浏览器窗口大小查看响应式效果</li>
          <li>悬停和点击元素体验交互动画</li>
          <li>观察进度条和统计数据的实时更新</li>
        </ul>
      </div>

      <div class="tech-stack">
        <h3>技术栈</h3>
        <div class="tech-tags">
          <span class="tech-tag">HTML5</span>
          <span class="tech-tag">CSS3</span>
          <span class="tech-tag">JavaScript ES6+</span>
          <span class="tech-tag">CSS Grid</span>
          <span class="tech-tag">Flexbox</span>
          <span class="tech-tag">CSS Variables</span>
          <span class="tech-tag">Backdrop Filter</span>
          <span class="tech-tag">CSS Animations</span>
          <span class="tech-tag">Responsive Design</span>
          <span class="tech-tag">Font Awesome</span>
        </div>
      </div>
    </div>

    <script>
      // 简单的入场动画
      document.addEventListener('DOMContentLoaded', () => {
        const container = document.querySelector('.demo-container')
        container.style.opacity = '0'
        container.style.transform = 'translateY(30px)'

        setTimeout(() => {
          container.style.transition = 'all 0.6s ease-out'
          container.style.opacity = '1'
          container.style.transform = 'translateY(0)'
        }, 100)
      })
    </script>
  </body>
</html>
