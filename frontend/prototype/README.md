# MediPaka 前端原型图

这是 MediPaka 媒体自动化系统的前端界面原型图，展示了现代化、具有质感的用户界面设计。

## 🎨 设计特色

### 年轻化活力设计 ✨

- **渐变色彩系统** 紫色、粉色、青色的活力配色
- **动态背景** 流动的渐变背景动画
- **彩虹Logo** 多彩渐变文字效果
- **发光按钮** 悬停时的光泽扫过效果
- **粒子动画** 卡片悬停时的粒子特效
- **波纹反馈** 按钮点击时的涟漪动画
- **弹性交互** 缩放和弹跳动画效果

### 现代化UI设计

- 毛玻璃效果 增强视觉层次
- 微交互动画 提升用户体验
- 一致的设计语言 贯穿整个应用
- 更紧凑的卡片布局
- 更高的信息密度
- 更清爽的视觉效果
- 更好的屏幕空间利用
- **卡片/列表视图切换** 默认卡片视图，支持列表切换
- Tab导航功能
- 主题切换功能
- 响应式布局

### 毛玻璃效果增强

顶部导航栏:添加了半透明背景和模糊效果，使其更加轻盈搜索框增加了毛玻璃效果和细微的边框高光，提升精致感过滤器选项:应用了微妙的模糊效果和细边框，使其漂浮在内容之上。卡片背景:将原来的纯色渐变背景替换为半透明毛玻璃效果，提供更现代的质底部导航栏:添加了深度模糊效果和顶部阴影，营造漂浮感

### 设计细节优化

增加了微妙的透明度层次，使界面更有深度为图片底部添加了渐变阴影，使文字更易阅读增强了按钮的视觉效果，加入了更精致的阴影添加了全局毛玻璃覆盖层，造整体深度感改进了动效设计，使交互更加流畅自然优化了激活状态的视觉反馈，如导航栏图标上移效果

### 响应式设计

- 桌面端优先 的设计理念
- 移动端完全适配 包含独立的移动端界面
- 自动设备检测 和界面切换
- 触摸优化 支持手势操作

### 主题系统

- 明暗主题切换 支持系统跟随
- CSS变量驱动 的主题系统
- 平滑过渡动画 主题切换效果
- 本地存储 记住用户偏好

## 📱 界面结构

### 桌面端布局

```
┌──────────────────────────────────────────────┐
│  logo                        主题/设置/账号/退出│
├─────────────────────────────────────────────
|                  导航栏                      |
──────────────────────────────────────────────
│  子导航栏 / Tab切换                            │
│                主内容区                       │
│                                              │
├──────────────────────────────────────────────┤
```

### 移动端布局

```
┌──────────────────────────────────────────────┐
│                  顶部栏                      │
├──────────────────────────────────────────────┤
│                                              │
│                主内容区                      │
│                                              │
├──────────────────────────────────────────────┤
│                底部导航                      │
└──────────────────────────────────────────────┘
```

## 🏠 页面功能展示

### 0.登录页面 (Login)

- 用户名密码登录

### 1. 仪表盘 (Dashboard)

- 统计卡片 显示系统关键指标
- 下载趋势图表 可视化数据展示
- 最近活动 实时更新列表
- 快速操作 常用功能入口

### 2. 榜单推荐 (Recommend)

- TMDB热门电影
- TMDB热门电视剧
- 豆瓣热门电影
- 豆瓣热门电视剧

### 3. 资源搜索 (Search)

- **大图展示优先** 突出显示媒体海报，视觉冲击力强
- **评分系统** 显著展示TMDB/豆瓣评分，帮助用户快速判断质量
- **年度标识** 醒目的年份标签，便于识别新旧内容
- **悬停交互** 鼠标悬停才显示操作按钮，界面更简洁
- **卡片视图优先** 默认使用大卡片展示搜索结果
- **视图切换** 支持卡片和列表两种展示方式
- **智能标签** 质量、来源、格式的彩色分类标签
- **多源搜索** 聚合各种资源站点
  - 网盘资源 (云盘转存按钮)
  - Nullbr BT站点 (下载按钮)
  - Telegram 频道搜索 (TG标识)
  - 自定义站点支持
- **悬停操作**
  - 下载/转存按钮 (主要操作)
  - 订阅按钮 (添加到订阅列表)
  - 详情按钮 (查看完整信息)
- **智能过滤** 质量/字幕组/格式筛选
- **搜索历史** 记录常用搜索
-

### 4. 订阅管理 (Subscriptions)

- 多源订阅统计 TMDB/豆瓣/Mikan
- 订阅列表管理 增删改查操作
- 状态监控 各源状态独立监控 活跃/暂停/错误状态，显示订阅进度和完成状态
- 搜索过滤 快速定位订阅
- 操作控制 - 暂停、删除、刷新
- 进度显示 - 实时进度条和百分比
- 批量操作 多选管理功能
- 视图切换 - 卡片视图和列表视图自由切换

### 5. 下载队列 (Downloads) 🎯

- **卡片视图优先** 默认使用卡片展示，更直观美观
- **视图切换** 支持卡片和列表两种展示方式
- **海报展示** 每个下载任务显示媒体海报
- **彩色进度条** 渐变色进度条，视觉效果更佳
- **实时进度监控** 下载状态展示
- **任务控制** 暂停/恢复/删除操作
- **速度统计** 下载速度和ETA
- **批量操作** 多选管理功能
- **来源标识** 显示下载器来源（qBittorrent、Aria2等）
- **状态管理** 下载中、已完成、已暂停、错误状态
- **悬停特效** 卡片悬停时的动画和阴影效果

### 5. 系统设置 (Settings)

- 分类清晰 -
  Tab导航将设置按功能分类，分为基础设置、重命名规则、通知设置、高级选项
- 账号配置 各平台API密钥管理/各网盘账号配置
- 下载器设置 QB/TR/Aria2配置/下载路径管理
- 重命名规则 TMDB元数据模板/自定义命名格式/多语言支持/特殊字符处理
- 通知配置 Telegram/Webhook/微信 推送
- 即时保存 每个分类独立保存设置
- 危险操作 明确标识高风险操作

## 🎯 交互特性

### 导航系统

- 侧边栏导航 (桌面端) 固定式导航菜单
- 抽屉导航 (移动端) 左滑呼出菜单
- 底部导航 (移动端) Tab式快速切换
- 面包屑导航 显示当前位置

### 动画效果

- 页面切换 淡入淡出过渡
- 卡片悬停 阴影和位移效果
- 按钮反馈 点击缩放和颜色变化
- 加载状态 旋转和脉冲动画

### 触摸优化

- 防双击缩放 移动端体验优化
- 触摸反馈 按压透明度变化
- 手势支持 滑动和长按操作
- 安全区域 适配异形屏幕

#### 设计特色

- 动态进度条 带有光泽动画效果
- 分层信息 主要信息和详细信息分离
- 操作反馈 按钮点击和状态变化动画

## 🔧 技术实现

### HTML结构

- 语义化标签 提升可访问性
- 模块化组件 便于维护和扩展
- SEO友好 合理的标题层级
- 无障碍支持 ARIA标签和键盘导航

### CSS架构

- CSS变量系统 统一管理设计令牌
- BEM命名规范 清晰的样式组织
- Flexbox/Grid布局 现代化布局方案
- 渐进增强 优雅降级支持

## 📂 文件结构

```
frontend/prototype/
├── index.html          # 主页面文件 - 完整的HTML结构和所有页面内容
├── styles.css          # 样式表文件 - 现代化设计和响应式布局
├── script.js           # 交互脚本 - 页面导航、主题切换、动态效果
├── demo.html           # 演示页面 - 功能介绍和使用说明
├── start.py            # 启动脚本 - 一键启动本地服务器
└── README.md          # 说明文档 - 详细的设计说明和使用指南
```

## 🚀 快速开始

### 一键启动 (推荐)

```bash
# 使用内置启动脚本 (自动打开浏览器)
python start.py

# 查看帮助
python start.py -h
```

### 手动启动

1. **直接打开**: 在浏览器中打开 `demo.html` 或 `index.html`
2. **本地服务器**:

   ```bash
   # 使用 Python
   python -m http.server 8080

   # 使用 Node.js
   npx serve .

   # 使用 Live Server (VS Code 扩展)
   ```

### 访问地址

- **演示页面**: `demo.html` - 功能介绍和使用说明
- **完整原型**: `index.html` - 完整的界面原型图

### 功能演示

- **页面导航**: 点击侧边栏或底部导航切换页面
- **主题切换**: 点击右上角月亮/太阳图标切换明暗主题
- **响应式**: 调整浏览器窗口大小查看移动端适配
- **交互动画**: 悬停和点击元素查看动画效果
- **实时更新**: 进度条和统计数据会自动更新

### 设计特色展示

- **毛玻璃效果**: 导航栏、卡片、弹窗等使用半透明背景
- **微交互**: 按钮点击、卡片悬停、页面切换动画
- **现代化布局**: Grid 和 Flexbox 响应式布局
- **一致性设计**: 统一的颜色系统、字体、间距

## 📱 移动端适配

### 断点系统

- xs: < 480px (小屏手机)
- sm: 480px - 768px (大屏手机)
- md: 768px - 1024px (平板)
- lg: 1024px - 1200px (小屏笔记本)
- xl: > 1200px (桌面显示器)

### 移动端特性

- 底部导航栏 符合拇指操作习惯
- 侧边抽屉菜单 节省屏幕空间
- 触摸友好 44px最小点击区域
- 安全区域适配 支持刘海屏等异形屏

## 🔧 技术实现详解

### HTML 结构设计

- **语义化标签**: 使用 `<header>`, `<nav>`, `<main>`, `<section>` 等语义化标签
- **可访问性**: 添加 ARIA 标签和键盘导航支持
- **SEO 友好**: 合理的标题层级和 meta 标签
- **模块化组件**: 每个功能模块独立的 HTML 结构

### CSS 架构特色

- **CSS 变量系统**: 统一管理颜色、尺寸、动画等设计令牌
- **BEM 命名规范**: 清晰的样式组织和命名约定
- **现代化布局**: Flexbox 和 Grid 布局方案
- **毛玻璃效果**: `backdrop-filter: blur()` 实现半透明模糊效果
- **响应式设计**: 移动优先的断点系统
- **动画系统**: CSS 过渡和关键帧动画

### JavaScript 功能模块

- **类组织**: 使用 ES6 Class 组织代码结构
- **事件管理**: 统一的事件监听和处理机制
- **状态管理**: 本地状态管理和持久化
- **动画控制**: 程序化控制动画和过渡效果
- **模拟数据**: 实时数据更新和进度模拟

### 响应式断点

```css
/* 移动端优先设计 */
@media (max-width: 480px) /* 小屏手机 */ @media (max-width: 768px) /* 大屏手机/小平板 */ @media (max-width: 1024px) /* 平板 */ @media (max-width: 1200px); /* 小屏笔记本 */
/* 1200px+ 桌面显示器 */
```

## 🎨 设计系统

### 颜色系统

```css
/* 主色调 */
--primary-color: #6366f1 /* 品牌主色 */ --success-color: #10b981 /* 成功状态 */
  --warning-color: #f59e0b /* 警告状态 */ --error-color: #ef4444 /* 错误状态 */ /* 背景色 */
  --bg-primary: #0f172a /* 主背景 */ --bg-glass: rgba(255, 255, 255, 0.1) /* 毛玻璃 */;
```

### 动画系统

- **过渡动画**: 0.3s cubic-bezier(0.4, 0, 0.2, 1)
- **悬停效果**: transform: translateY(-4px)
- **点击反馈**: transform: scale(0.95)
- **页面切换**: 淡入淡出 + 位移动画

## 🔮 未来扩展

### 计划功能

- PWA支持 离线使用和推送通知
- 国际化 多语言支持
- 个性化 用户自定义主题和布局
- 数据可视化 更丰富的图表和统计
- 拖拽排序 自定义布局和组件顺序
- 快捷键 键盘快捷操作支持

### 技术升级路径

- **Vue 3 集成**: 组件化开发和响应式数据
- **TypeScript**: 类型安全和更好的开发体验
- **Vite 构建**: 快速开发和热更新
- **Pinia 状态管理**: 现代化的状态管理方案
- **Chart.js**: 数据可视化图表库

### 性能优化

- **代码分割**: 按需加载页面和组件
- **图片优化**: WebP 格式和懒加载
- **缓存策略**: Service Worker 和资源缓存
- **CDN 加速**: 静态资源 CDN 分发
