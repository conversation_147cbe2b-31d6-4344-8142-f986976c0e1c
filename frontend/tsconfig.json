{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": [
    "src/**/__tests__/*",
    "node_modules",
    "dist",
    "build",
    "coverage",
    "**/*.temp.*",
    "**/.temp/**"
  ],
  "compilerOptions": {
    // Project Configuration
    "composite": true,
    "incremental": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    // Module Resolution
    "baseUrl": ".",
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,

    // Path Mapping (mirrors Vite config for consistent IDE support)
    "paths": {
      "@/*": ["./src/*"],
      "@styles/*": ["./src/styles/*"],
      "@components/*": ["./src/components/*"],
      "@views/*": ["./src/views/*"],
      "@stores/*": ["./src/stores/*"],
      "@types/*": ["./src/types/*"]
    },

    // Type Checking - Enhanced Safety
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,

    // Performance & Compatibility
    "skipLibCheck": true,
    "isolatedModules": true,

    // Type Definitions
    "types": ["vite/client", "node"]
  }
}
