{"name": "medipaka-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "lint:quiet": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --quiet", "format": "prettier --write .", "format:check": "prettier --check .", "lint-staged": "lint-staged"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.11.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.32.0", "@iconify/json": "^2.2.363", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "^5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6", "vue-tsc": "^3.0.4"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix --quiet", "prettier --write"], "*.{css,scss,sass,less}": ["prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}