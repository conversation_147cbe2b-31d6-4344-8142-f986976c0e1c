import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

/**
 * Vite 配置文件
 */
export default defineConfig({
  // Vue插件配置
  plugins: [
    vue(),
    // 自动导入API (仅Element Plus相关)
    AutoImport({
      resolvers: [ElementPlusResolver()],
      dts: true, // 生成类型声明文件
    }),
    // 自动导入组件
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true, // 生成类型声明文件
    }),
  ],

  // 路径解析配置
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@styles': fileURLToPath(new URL('./src/styles', import.meta.url)),
      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),
      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
      '@types': fileURLToPath(new URL('./src/types', import.meta.url)),
    },
  },

  // 开发服务器配置
  server: {
    port: 3000,
    host: true, // 允许外部访问
    open: false, // 不自动打开浏览器
    cors: true, // 启用CORS
    strictPort: true, // 端口被占用时不自动尝试下一个端口
    hmr: {
      overlay: true, // 显示错误覆盖层
    },
  },

  // CSS预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // 静默弃用警告
        silenceDeprecations: ['legacy-js-api'],
      },
    },
  },

  // 构建配置
  build: {
    outDir: 'dist',
    // 静态资源目录
    assetsDir: 'assets',
    // 生成源码映射
    sourcemap: false,
    // 代码分割阈值
    chunkSizeWarningLimit: 1000,
    // Rollup配置
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'element-plus-vendor': ['element-plus'],
          utils: ['@/utils/index'],
        },
      },
    },
  },
})
