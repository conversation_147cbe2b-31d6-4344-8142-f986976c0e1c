# 构建输出目录
dist/
build/

# 依赖目录
node_modules/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建 / 生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# 编辑器目录和文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS 生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 包管理器锁定文件（保留一个）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# Vite 缓存
.vite/

# 测试输出
test-results/
playwright-report/
playwright/.cache/

# 文档生成
docs/.vitepress/cache
docs/.vitepress/dist

# 其他忽略文件
*.min.js
*.min.css
*.bundle.js
*.chunk.js
*.map

# 配置文件（某些情况下不需要格式化）
# *.config.js
# *.config.ts

# 静态资源
public/
static/
assets/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 图片文件（通常不需要格式化）
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.webp

# 字体文件
*.woff
*.woff2
*.ttf
*.eot
*.otf

# 视频文件
*.mp4
*.avi
*.mov
*.wmv
*.flv

# 音频文件
*.mp3
*.wav
*.ogg
*.m4a

# 证书文件
*.pem
*.key
*.crt
*.cert

# 许可证文件
LICENSE*
LICENCE*

# 变更日志
CHANGELOG*
HISTORY*

# 贡献指南
CONTRIBUTING*

# 安全策略
SECURITY*

# 代码所有者
CODEOWNERS

# GitHub 模板
.github/

# GitLab 模板
.gitlab/

# 持续集成配置
.travis.yml
.circleci/
.github/workflows/
.gitlab-ci.yml
appveyor.yml
azure-pipelines.yml

# Docker 文件
Dockerfile*
docker-compose*.yml
.dockerignore

# Kubernetes 配置
*.yaml
*.yml

# Terraform 文件
*.tf
*.tfvars
*.tfstate
*.tfstate.backup

# 云配置
.aws/
.gcp/
.azure/

# 本地开发配置
.local
.dev
.development

# 生产配置
.prod
.production

# 测试配置
.test
.testing

# 临时文件
*.tmp
*.temp
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# IDE 配置
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 编辑器配置（保留）
# .editorconfig

# Git 配置
.gitignore
.gitattributes
.gitmodules

# 包管理器配置
.npmrc
.yarnrc
.pnpmrc

# 浏览器列表配置
.browserslistrc

# Babel 配置
.babelrc*
babel.config.*

# PostCSS 配置
postcss.config.*

# Tailwind 配置
tailwind.config.*

# ESLint 配置
.eslintrc*
eslint.config.*

# Prettier 配置（当前文件）
.prettierrc*
prettier.config.*

# StyleLint 配置
.stylelintrc*
stylelint.config.*

# Jest 配置
jest.config.*

# Vitest 配置
vitest.config.*

# Playwright 配置
playwright.config.*

# Cypress 配置
cypress.config.*

# 其他测试配置
*.test.config.*
*.spec.config.*
